import math
import logging
import numpy as np
import itertools
from pydantic import BaseModel, Field, validator
from typing import Dict, List, Optional, Any, Tuple

from ..utils.helpers import setup_path_for_imports

setup_path_for_imports()

from utils import constants as const

log = logging.getLogger(__name__)
epsilon = const.EPSILON

def get_transformer_data_for_limits(data) -> dict:
    """
    Extrai dados do transformador necessários para determinar limites SUT/EPS e seleção de fonte.

    Args:
        data: Objeto de dados do transformador (NoLoadLossesInput ou LoadLossesInput)

    Returns:
        Dict com tipo_transformador, grupo_ligacao e tensões do DUT
    """
    return {
        "tipo_transformador": getattr(data, 'tipo_transformador', ''),
        "grupo_ligacao": getattr(data, 'grupo_ligacao', ''),
        "tensao_at_kv": getattr(data, 'tensao_at_kv', 0),
        "tensao_bt_kv": getattr(data, 'tensao_bt_kv', 0),
        "tensao_terciario_kv": getattr(data, 'tensao_terciario_kv', 0)
    }

def normalize_transformer_type(tipo_transformador: str) -> str:
    """
    Normaliza o tipo de transformador para compatibilidade com lógica existente.

    Args:
        tipo_transformador: Tipo original do transformador

    Returns:
        Tipo normalizado ("Trifásico" ou "Monofásico")
    """
    if not tipo_transformador:
        return "Trifásico"  # Padrão

    tipo_lower = tipo_transformador.lower()

    # Mapear autotransformadores para seus tipos base
    if "autotransformador" in tipo_lower:
        if "monofásico" in tipo_lower or "monofasico" in tipo_lower:
            return "Monofásico"
        else:
            return "Trifásico"

    # Mapear tipos básicos
    if "monofásico" in tipo_lower or "monofasico" in tipo_lower:
        return "Monofásico"
    else:
        return "Trifásico"


# --- Pydantic Models ---
class NoLoadLossesInput(BaseModel):
    perdas_vazio_ui: float = Field(..., gt=0)
    peso_nucleo_ui: float = Field(..., gt=0)
    corrente_excitacao_ui: float = Field(..., gt=0)
    inducao_ui: float = Field(..., gt=0)
    corrente_exc_1_1_ui: Optional[float] = Field(None, gt=0)
    corrente_exc_1_2_ui: Optional[float] = Field(None, gt=0)
    frequencia: float = Field(..., gt=0)
    tensao_bt_kv: float = Field(..., gt=0)
    corrente_nominal_bt: float = Field(..., gt=0)
    tipo_transformador: str = "Trifásico"
    grupo_ligacao: str = ""
    steel_type: str = "M4"
    potencia_mva: float = Field(..., gt=0)
    tensao_terciario_kv: Optional[float] = Field(None, gt=0)
    corrente_nominal_terciario: Optional[float] = Field(None, gt=0)

    @validator('tipo_transformador')
    def tipo_transformador_must_be_valid(cls, v):
        # Aceita os novos tipos e mapeia para os tipos básicos para compatibilidade
        valid_types = ["Trifásico", "Monofásico", "Autotransformador", "Autotransformador Monofásico"]
        if v not in valid_types:
            raise ValueError(f'tipo_transformador deve ser um dos seguintes: {", ".join(valid_types)}')
        return v

    @validator('steel_type')
    def steel_type_must_be_valid(cls, v):
        if v not in ["M4", "H110-27"]:
            raise ValueError('steel_type deve ser "M4" ou "H110-27"')
        return v


class LoadLossesInput(BaseModel):
    temperatura_referencia: int = Field(..., ge=25)
    perdas_carga_kw_u_min: float = Field(..., gt=0)
    perdas_carga_kw_u_nom: float = Field(..., gt=0)
    perdas_carga_kw_u_max: float = Field(..., gt=0)
    potencia_mva: float = Field(..., gt=0)
    impedancia: float = Field(..., gt=0)
    tensao_at_kv: float = Field(..., gt=0)
    tensao_at_tap_maior_kv: float = Field(..., gt=0)
    tensao_at_tap_menor_kv: float = Field(..., gt=0)
    impedancia_tap_maior: float = Field(..., gt=0)
    impedancia_tap_menor: float = Field(..., gt=0)
    corrente_nominal_at_a: float = Field(..., gt=0)
    corrente_nominal_at_tap_maior_a: float = Field(..., gt=0)
    corrente_nominal_at_tap_menor_a: float = Field(..., gt=0)
    tipo_transformador: str = "Trifásico"
    grupo_ligacao: str = ""
    perdas_vazio_kw_calculada: float = Field(..., gt=0)
    factor_cap_banc_overvoltage: float = Field(default=1.1, ge=1.0, le=2.0)
    tensao_terciario_kv: Optional[float] = Field(None, gt=0)
    corrente_nominal_terciario: Optional[float] = Field(None, gt=0)
    # Additional voltage fields for conditional load loss logic
    tensao_bt_kv: Optional[float] = Field(None, gt=0)
    classe_tensao_at: Optional[float] = Field(None, gt=0)
    classe_tensao_bt: Optional[float] = Field(None, gt=0)
    classe_tensao_terciario: Optional[float] = Field(None, gt=0)

    @validator('tipo_transformador')
    def tipo_transformador_must_be_valid(cls, v):
        # Aceita os novos tipos e mapeia para os tipos básicos para compatibilidade
        valid_types = ["Trifásico", "Monofásico", "Autotransformador", "Autotransformador Monofásico"]
        if v not in valid_types:
            raise ValueError(f'tipo_transformador deve ser um dos seguintes: {", ".join(valid_types)}')
        return v

# --- Helper Functions (No-Load) ---
def safe_float(value, default=0.0):
    try: return float(value) if value is not None and str(value).strip() != "" else default
    except (ValueError, TypeError): return default



def should_include_overload_scenarios(data: LoadLossesInput) -> bool:
    """
    Determines if 1.2 PU and 1.4 PU load loss scenarios should be included
    based on voltage conditions.

    The scenarios should only be displayed when ANY of the following conditions are met:
    1. ALTA TENSÃO (AT) voltage >= 230kV
    2. Tap+ kV voltage >= 230kV
    3. Tap- kV voltage >= 230kV
    4. BAIXA TENSÃO (BT) voltage >= 230kV
    5. TERCIÁRIO voltage >= 230kV
    6. OR if the voltage class of any of these windings >= 245kV

    Args:
        data: LoadLossesInput object containing voltage data

    Returns:
        bool: True if overload scenarios should be included, False otherwise
    """
    voltage_threshold = 230.0  # kV
    voltage_class_threshold = 245.0  # kV

    # Check voltage levels
    voltages_to_check = [
        data.tensao_at_kv,
        data.tensao_at_tap_maior_kv,
        data.tensao_at_tap_menor_kv,
        data.tensao_bt_kv,
        data.tensao_terciario_kv
    ]

    # Check if any voltage >= 230kV
    for voltage in voltages_to_check:
        if voltage is not None and voltage >= voltage_threshold:
            log.info(f"Overload scenarios included: voltage {voltage}kV >= {voltage_threshold}kV")
            return True

    # Check voltage classes
    voltage_classes_to_check = [
        data.classe_tensao_at,
        data.classe_tensao_bt,
        data.classe_tensao_terciario
    ]

    # Check if any voltage class >= 245kV
    for voltage_class in voltage_classes_to_check:
        if voltage_class is not None and voltage_class >= voltage_class_threshold:
            log.info(f"Overload scenarios included: voltage class {voltage_class}kV >= {voltage_class_threshold}kV")
            return True

    log.info("Overload scenarios excluded: no voltage or voltage class meets threshold requirements")
    return False

def check_ct_current_limit(current_a: float) -> Tuple[bool, str]:
    """
    Verifica se a corrente de teste excede o limite do transformador de corrente (CT).

    Args:
        current_a: Corrente de teste em amperes

    Returns:
        (is_critical: bool, status_msg: str)
    """
    # Validação de segurança para evitar comparações com None
    if current_a is None:
        return False, "OK - Corrente não especificada."

    if current_a > const.CT_CURRENT_LIMIT + epsilon:
        status_msg = f"🚫 TESTE IMPOSSÍVEL: CRÍTICO: Corrente de teste ({current_a:.1f}A) excede limite do CT ({const.CT_CURRENT_LIMIT:.0f}A). Teste impossível."
        log.error(status_msg)
        return True, status_msg
    return False, "OK - Corrente dentro do limite do CT."


def analyze_dut_winding_options(transformer_data: dict, vtest_kv: float) -> dict:
    """
    Analisa as opções de enrolamentos do DUT e suas características.

    Args:
        transformer_data: Dados do transformador (tipo_transformador, grupo_ligacao, tensões)
        vtest_kv: Tensão de teste em kV

    Returns:
        Dict com análise dos enrolamentos disponíveis
    """
    grupo_ligacao = transformer_data.get('grupo_ligacao', '').lower()
    tipo_transformador = transformer_data.get('tipo_transformador', '')

    # Extrair tensões do DUT se disponíveis
    # Validação de segurança para evitar comparações com None
    tensao_at = transformer_data.get('tensao_at_kv', 0) or 0
    tensao_bt = transformer_data.get('tensao_bt_kv', 0) or 0
    tensao_terciario = transformer_data.get('tensao_terciario_kv', 0) or 0

    winding_options = {
        'at_delta': False,
        'bt_delta': False,
        'terciario_delta': False,
        'terciario_has_neutral': False,
        'available_windings': []
    }

    # Analisar grupo de ligação para identificar deltas
    if grupo_ligacao:
        grupo_lower = grupo_ligacao.lower()

        # AT em delta (primeira letra D)
        if grupo_lower.startswith('d'):
            winding_options['at_delta'] = True

        # Identificar terciário delta (letra 'd' após números ou no final)
        # Exemplos: YNd1d0, Dyn3d1, Yna0d1
        import re
        # Detectar delta: procurar por 'd' seguido de números (incluindo casos como 'd1')
        if 'd1' in grupo_lower or 'd3' in grupo_lower or 'd5' in grupo_lower or 'd7' in grupo_lower or 'd9' in grupo_lower or 'd11' in grupo_lower or 'd0' in grupo_lower:
            winding_options['terciario_delta'] = True


        # Identificar terciário com neutro (y, z após números)
        if 'y1' in grupo_lower or 'y0' in grupo_lower or 'z1' in grupo_lower or 'z0' in grupo_lower:
            winding_options['terciario_has_neutral'] = True

    # Montar lista de enrolamentos disponíveis com suas tensões
    if tensao_at > 0:
        winding_options['available_windings'].append({
            'type': 'AT',
            'voltage_kv': tensao_at,
            'is_delta': winding_options['at_delta'],
            'effective_voltage_kv': tensao_at
        })

    if tensao_bt > 0:
        winding_options['available_windings'].append({
            'type': 'BT',
            'voltage_kv': tensao_bt,
            'is_delta': winding_options['bt_delta'],
            'effective_voltage_kv': tensao_bt
        })

    if tensao_terciario > 0:
        # Para terciário com neutro, tensão efetiva é reduzida
        effective_voltage = tensao_terciario
        if winding_options['terciario_has_neutral']:
            effective_voltage = tensao_terciario / const.SQRT_3

        winding_options['available_windings'].append({
            'type': 'TERCIARIO',
            'voltage_kv': tensao_terciario,
            'is_delta': winding_options['terciario_delta'],
            'has_neutral': winding_options['terciario_has_neutral'],
            'effective_voltage_kv': effective_voltage
        })

    log.info(f"[DUT Analysis] Grupo: {grupo_ligacao}, Tipo: {tipo_transformador}")
    log.info(f"[DUT Analysis] Enrolamentos disponíveis: {len(winding_options['available_windings'])}")
    for winding in winding_options['available_windings']:
        log.info(f"[DUT Analysis] - {winding['type']}: {winding['voltage_kv']}kV (efetiva: {winding['effective_voltage_kv']:.1f}kV)")

    return winding_options


def select_optimal_sut_source_for_dut(vtest_kv: float, transformer_data: dict) -> Tuple[str, List[float], str]:
    """
    Seleciona a melhor fonte SUT baseada na configuração do DUT e capacidades das fontes.

    Args:
        vtest_kv: Tensão de teste em kV
        transformer_data: Dados do transformador (tipo, grupo de ligação, tensões)

    Returns:
        (source_type: str, available_voltages_v: List[float], display_msg: str)
    """
    # Determinar tipo de fonte SUT/EPS baseado no DUT
    sut_eps_type = const.determine_sut_eps_type(transformer_data)

    # Determinar limite máximo da fonte baseado no tipo
    if sut_eps_type == "Monofásico":
        sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000 / const.SQRT_3  # 80kV
    else:
        sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000  # 140kV

    # Analisar configuração do DUT
    dut_analysis = analyze_dut_winding_options(transformer_data, vtest_kv)

    # Verificar se pode atingir 1.1 PU e 1.2 PU
    vtest_1_1_pu = vtest_kv * 1.1
    vtest_1_2_pu = vtest_kv * 1.2

    can_reach_1_1_pu = vtest_1_1_pu <= sut_max_voltage_kv
    can_reach_1_2_pu = vtest_1_2_pu <= sut_max_voltage_kv

    # Lógica de seleção inteligente baseada na capacidade de atingir 1.0-1.2 PU

    # Verificar se terciário da fonte pode atingir 1.0-1.2 PU
    vtest_1_2_pu = vtest_kv * 1.2
    terc_max_voltage_kv = const.SUT_TERC_MAX_VOLTAGE / 1000  # 14kV

    can_tertiary_reach_1_2_pu = vtest_1_2_pu <= terc_max_voltage_kv

    # Caso 1: Usar terciário da fonte SOMENTE se puder atingir 1.2 PU
    if can_tertiary_reach_1_2_pu:
        # Usar terciário da fonte (pode atingir 1.2 PU)
        available_voltages_v = [
            const.SUT_TERC_MIN_VOLTAGE,      # 3.5kV
            const.SUT_TERC_INTERMEDIATE_VOLTAGE,  # 7kV
            const.SUT_TERC_MAX_VOLTAGE       # 14kV
        ]
        display_msg = f"usando terciário do SUT (pode atingir 1.2PU={vtest_1_2_pu:.1f}kV, fonte {sut_eps_type})"
        return "SUT_TERC", available_voltages_v, display_msg

    # Caso 2: Usar AT da fonte (terciário não pode atingir 1.2 PU)
    else:
        # Verificar se fonte pode atingir 1.1 e 1.2 PU
        pu_status = []
        if not can_reach_1_1_pu:
            pu_status.append("1.1PU impossível")
        if not can_reach_1_2_pu:
            pu_status.append("1.2PU impossível")

        available_voltages_v = [float(t) for t in np.arange(
            const.SUT_AT_MIN_VOLTAGE,
            min(const.SUT_AT_MAX_VOLTAGE, sut_max_voltage_kv * 1000) + const.SUT_AT_STEP_VOLTAGE,
            const.SUT_AT_STEP_VOLTAGE
        ) if t > epsilon]

        pu_warning = f" ({', '.join(pu_status)})" if pu_status else ""
        terc_reason = f"terciário não pode atingir 1.2PU={vtest_1_2_pu:.1f}kV > {terc_max_voltage_kv:.1f}kV"
        display_msg = f"usando AT do SUT ({terc_reason}, fonte {sut_eps_type}{pu_warning})"
        return "SUT_AT", available_voltages_v, display_msg


def select_sut_output_voltage_source(vtest_kv: float) -> Tuple[str, List[float], str]:
    """
    FUNÇÃO LEGACY - Mantida para compatibilidade.

    Determina qual saída do SUT usar baseado na tensão de teste.
    Regra simples: Vtest < 14kV → usar TERCIÁRIO do SUT, Vtest ≥ 14kV → usar AT do SUT

    Args:
        vtest_kv: Tensão de teste em kV

    Returns:
        (source_type: str, available_voltages_v: List[float], display_msg: str)
        source_type: "SUT_TERC" ou "SUT_AT"
        available_voltages_v: Lista de tensões disponíveis em V
        display_msg: Mensagem para exibição
    """
    THRESHOLD_KV = 14.0

    if vtest_kv < THRESHOLD_KV:
        # Usar TERCIÁRIO do SUT
        available_voltages_v = [
            const.SUT_TERC_MIN_VOLTAGE,      # 3.5kV
            const.SUT_TERC_INTERMEDIATE_VOLTAGE,  # 7kV
            const.SUT_TERC_MAX_VOLTAGE       # 14kV
        ]
        display_msg = f"usando o terciário do SUT (Vtest={vtest_kv:.1f}kV < 14kV)"
        return "SUT_TERC", available_voltages_v, display_msg
    else:
        # Usar AT do SUT
        available_voltages_v = [float(t) for t in np.arange(
            const.SUT_AT_MIN_VOLTAGE,
            const.SUT_AT_MAX_VOLTAGE + const.SUT_AT_STEP_VOLTAGE,
            const.SUT_AT_STEP_VOLTAGE
        ) if t > epsilon]
        display_msg = f"usando AT do SUT (Vtest={vtest_kv:.1f}kV ≥ 14kV)"
        return "SUT_AT", available_voltages_v, display_msg


def select_optimal_sut_voltage(vtest_kv: float, available_voltages_v: List[float]) -> float:
    """
    Seleciona a tensão ótima do SUT para a tensão de teste.

    Args:
        vtest_kv: Tensão de teste em kV
        available_voltages_v: Tensões disponíveis em V

    Returns:
        selected_voltage_v: Tensão selecionada em V
    """
    vtest_v = vtest_kv * 1000

    # Encontrar tensões adequadas (>= vtest_v)
    adequate_voltages = [v for v in available_voltages_v if v >= vtest_v - epsilon]

    if adequate_voltages:
        # Usar a menor tensão adequada
        return min(adequate_voltages)
    elif available_voltages_v:
        # Se nenhuma é adequada, usar a maior disponível
        return max(available_voltages_v)
    else:
        # Fallback (não deveria acontecer)
        return vtest_v


def migrate_legacy_sut_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Migra dados antigos para incluir os novos campos de SUT.
    Garante compatibilidade com projetos salvos antes da atualização.
    """
    if not isinstance(data, dict):
        return data

    # Migrar dados de no-load losses
    if "sut_eps_analysis_results" in data:
        for scenario_key, scenario_data in data["sut_eps_analysis_results"].items():
            if isinstance(scenario_data, dict) and "taps_info" in scenario_data:
                # Adicionar campos ausentes nos taps_info
                for tap_info in scenario_data["taps_info"]:
                    if "sut_source_type" not in tap_info:
                        # Inferir tipo baseado na tensão (lógica de fallback)
                        tap_kv = tap_info.get("sut_tap_kv", 0)
                        tap_info["sut_source_type"] = "SUT_TERC" if tap_kv < 14.0 else "SUT_AT"
                        tap_info["is_ideal_tap"] = tap_info.get("is_ideal_tap", False)

                # Adicionar campos ausentes no scenario_data
                if "sut_source_type" not in scenario_data:
                    # Inferir baseado no primeiro tap
                    first_tap = scenario_data["taps_info"][0] if scenario_data["taps_info"] else {}
                    first_tap_kv = first_tap.get("sut_tap_kv", 15.0)  # Default para AT
                    scenario_data["sut_source_type"] = "SUT_TERC" if first_tap_kv < 14.0 else "SUT_AT"
                    scenario_data["sut_display_msg"] = f"usando {'o terciário' if first_tap_kv < 14.0 else 'AT'} do SUT (migrado)"
                    scenario_data["ideal_tap_kv"] = first_tap.get("sut_tap_kv")

    # Migrar dados de load losses
    if "cenarios_perdas_carga" in data:
        for scenario_data in data["cenarios_perdas_carga"]:
            if "sut_eps_analysis" in scenario_data:
                for tap_info in scenario_data["sut_eps_analysis"]:
                    if "sut_source_type" not in tap_info:
                        # Inferir tipo baseado na tensão
                        tap_kv = tap_info.get("sut_tap_kv", 0)
                        tap_info["sut_source_type"] = "SUT_TERC" if tap_kv < 14.0 else "SUT_AT"
                        tap_info["sut_display_msg"] = f"usando {'o terciário' if tap_kv < 14.0 else 'AT'} do SUT (migrado)"

    return data


def check_sut_voltage_limit(voltage_kv: float, transformer_data: Optional[Dict[str, str]] = None) -> Tuple[bool, str]:
    """
    Verifica se a tensão de teste excede o limite máximo de saída do SUT baseado no tipo de transformador.

    Args:
        voltage_kv: Tensão de teste em kV
        transformer_data: Dados do transformador para determinar tipo SUT/EPS

    Returns:
        (is_critical: bool, status_msg: str)
    """
    # Validação de segurança para evitar comparações com None
    if voltage_kv is None:
        return False, "OK - Tensão não especificada."
    if transformer_data:
        sut_eps_type = const.determine_sut_eps_type(transformer_data)
        if sut_eps_type == "Monofásico":
            # Para Monofásico, limite é 140kV / √3 = 80.83kV (fase-neutro)
            sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000 / const.SQRT_3
        else:
            # Para Bifásico e Trifásico, usar limite padrão (linha-linha)
            sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000
    else:
        # Fallback para compatibilidade
        sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000

    if voltage_kv > sut_max_voltage_kv + epsilon:
        status_msg = f"🚫 TESTE IMPOSSÍVEL: CRÍTICO: Tensão de teste ({voltage_kv:.1f}kV) excede limite máximo do SUT {sut_eps_type if transformer_data else ''} ({sut_max_voltage_kv:.1f}kV). Teste impossível."
        log.error(status_msg)
        return True, status_msg
    return False, "OK - Tensão dentro do limite do SUT."


def select_optimal_dut_winding_for_test(data: NoLoadLossesInput) -> Tuple[bool, str, str]:
    """
    Seleciona o melhor enrolamento do DUT para teste baseado na configuração e capacidades das fontes.

    Args:
        data: Dados de entrada para perdas em vazio

    Returns:
        (usar_terciario: bool, status_msg: str, winding_choice: str)
        usar_terciario: True se deve usar terciário, False se deve usar BT
        status_msg: Mensagem explicativa da decisão
        winding_choice: "Terciário" ou "BT"
    """

    # Obter dados do transformador
    transformer_data = get_transformer_data_for_limits(data)
    sut_eps_type = const.determine_sut_eps_type(transformer_data)

    # Analisar configuração do DUT
    dut_analysis = analyze_dut_winding_options(transformer_data, data.tensao_bt_kv)
    log.info(f"[DUT Selection] Tipo fonte: {sut_eps_type}")
    log.info(f"[DUT Selection] Terciário delta: {dut_analysis.get('terciario_delta', False)}")
    log.info(f"[DUT Selection] Terciário neutro: {dut_analysis.get('terciario_has_neutral', False)}")

    # Determinar limite máximo SUT baseado no tipo
    if sut_eps_type == "Monofásico":
        sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000 / const.SQRT_3  # 80kV
    else:
        sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000  # 140kV

    log.info(f"[DUT Selection] Limite SUT: {sut_max_voltage_kv:.1f}kV")

    # Verificar se BT pode atingir 1.1 e 1.2 PU
    # Validação de segurança para evitar comparações com None
    tensao_bt_kv_safe_3 = data.tensao_bt_kv if data.tensao_bt_kv is not None else 0.0
    bt_1_1_pu = tensao_bt_kv_safe_3 * 1.1
    bt_1_2_pu = tensao_bt_kv_safe_3 * 1.2
    bt_can_reach_1_1_pu = bt_1_1_pu <= sut_max_voltage_kv
    bt_can_reach_1_2_pu = bt_1_2_pu <= sut_max_voltage_kv

    # Verificar se terciário está disponível e é viável
    # Validação de segurança para evitar comparações com None
    tensao_terciario_safe = data.tensao_terciario_kv if data.tensao_terciario_kv is not None else 0.0
    corrente_nominal_terciario_safe = data.corrente_nominal_terciario if data.corrente_nominal_terciario is not None else 0.0

    terciario_available = (data.tensao_terciario_kv and data.corrente_nominal_terciario and
                          tensao_terciario_safe > epsilon and corrente_nominal_terciario_safe > epsilon)

    # Caso 1: BT excede limites da fonte - OBRIGATÓRIO usar terciário
    if not bt_can_reach_1_1_pu or not bt_can_reach_1_2_pu:
        if terciario_available:
            problemas = []
            if not bt_can_reach_1_1_pu:
                problemas.append(f"1.1PU ({bt_1_1_pu:.1f}kV)")
            if not bt_can_reach_1_2_pu:
                problemas.append(f"1.2PU ({bt_1_2_pu:.1f}kV)")

            status_msg = f"⚠️ USO OBRIGATÓRIO DO TERCIÁRIO: BT {', '.join(problemas)} excede limite fonte {sut_eps_type} ({sut_max_voltage_kv:.1f}kV). Usando TERCIÁRIO ({data.tensao_terciario_kv:.1f}kV)."
            log.warning(status_msg)
            return True, status_msg, "Terciário"
        else:
            status_msg = f"🚨 CRÍTICO: BT excede limites fonte {sut_eps_type} e terciário indisponível."
            log.error(status_msg)
            return False, status_msg, "BT"

    # Caso 2: Lógica inteligente - verificar se terciário é melhor opção
    if terciario_available:
        # Verificar se terciário tem delta (melhor capacidade de potência)
        terciario_dut = next((w for w in dut_analysis['available_windings'] if w['type'] == 'TERCIARIO'), None)

        if terciario_dut and terciario_dut.get('is_delta', False):
            # Terciário delta - verificar se tensão é adequada e fonte pode ser otimizada
            # Validação de segurança para evitar comparações com None
            tensao_terciario_kv_safe = data.tensao_terciario_kv if data.tensao_terciario_kv is not None else 0.0
            if tensao_terciario_kv_safe < 50:  # Tensão baixa favorece terciário
                # Para fonte bifásica/trifásica, terciário baixo é melhor que BT alta
                # Validação de segurança para evitar comparações com None
                tensao_bt_safe = data.tensao_bt_kv if data.tensao_bt_kv is not None else 0.0
                if sut_eps_type in ["Bifásico", "Trifásico"] and tensao_bt_safe > 50:
                    status_msg = f"✅ OTIMIZAÇÃO INTELIGENTE: Usando TERCIÁRIO delta ({data.tensao_terciario_kv:.1f}kV) ao invés de BT ({data.tensao_bt_kv:.1f}kV) para melhor aproveitamento da fonte {sut_eps_type}."
                    log.info(status_msg)

                    return True, status_msg, "Terciário"

        elif terciario_dut and terciario_dut.get('has_neutral', False):
            # Terciário com neutro - verificar se tensão efetiva é adequada
            tensao_efetiva = terciario_dut['effective_voltage_kv']
            # Validação de segurança para evitar comparações com None
            tensao_bt_kv_safe_2 = data.tensao_bt_kv if data.tensao_bt_kv is not None else 0.0
            if tensao_efetiva < tensao_bt_kv_safe_2 * 0.5:  # Tensão efetiva muito baixa
                status_msg = f"⚠️ TERCIÁRIO COM NEUTRO INADEQUADO: Tensão efetiva ({tensao_efetiva:.1f}kV) muito baixa. Usando BT ({tensao_bt_kv_safe_2:.1f}kV)."
                log.warning(status_msg)
                return False, status_msg, "BT"

    # Caso 3: Usar BT (padrão)
    status_msg = f"OK - Usando BT ({data.tensao_bt_kv:.1f}kV) para todos os níveis. Fonte {sut_eps_type} adequada."
    log.info(status_msg)

    return False, status_msg, "BT"


def check_if_need_tertiary_for_all_levels(data: NoLoadLossesInput) -> Tuple[bool, str]:
    """
    FUNÇÃO LEGACY - Mantida para compatibilidade.
    Usa a nova lógica inteligente de seleção de enrolamento do DUT.
    """
    usar_terciario, status_msg, _ = select_optimal_dut_winding_for_test(data)
    return usar_terciario, status_msg

def interpolate_losses_table_data(table_name: str, induction: float, frequency: float, steel_type: str = "M4") -> float:
    if steel_type == "M4": table_data = const.potencia_magnet_data_M4 if table_name == "potencia_magnet" else const.perdas_nucleo_data_M4
    elif steel_type == "H110-27": table_data = const.potencia_magnet_data_H110_27 if table_name == "potencia_magnet" else const.perdas_nucleo_data_H110_27
    else: log.error(f"Aço desconhecido: {steel_type}. Retornando 0.0."); return 0.0
    if not table_data: log.error(f"Tabela '{table_name}' para '{steel_type}' vazia. Retornando 0.0."); return 0.0
    inductions, frequencies = sorted(list(set(k[0] for k in table_data.keys()))), sorted(list(set(k[1] for k in table_data.keys())))
    if not inductions or not frequencies: log.error(f"Listas de induções ou frequências vazias para tabela '{table_name}' ({steel_type}). Retornando 0.0."); return 0.0
    clamped_induction, clamped_frequency = max(inductions[0], min(inductions[-1], induction)), max(frequencies[0], min(frequencies[-1], frequency))
    if induction < inductions[0] or induction > inductions[-1] or frequency < frequencies[0] or frequency > frequencies[-1]:
        closest_i, closest_f = min(inductions, key=lambda x: abs(x - clamped_induction)), min(frequencies, key=lambda x: abs(x - clamped_frequency))
        return table_data.get((closest_i, closest_f), 0.0)
    i0, i1, f0, f1 = max((i for i in inductions if i <= clamped_induction), default=inductions[0]), min((i for i in inductions if i >= clamped_induction), default=inductions[-1]), max((f for f in frequencies if f <= clamped_frequency), default=frequencies[0]), min((f for f in frequencies if f >= clamped_frequency), default=frequencies[-1])
    if abs(i0 - i1) < epsilon and abs(f0 - f1) < epsilon: return table_data.get((i0, f0), 0.0)
    if abs(i0 - i1) < epsilon:
        v0, v1 = table_data.get((i0, f0), 0.0), table_data.get((i0, f1), 0.0)
        return v0 + (v1 - v0) * (clamped_frequency - f0) / (f1 - f0) if abs(f1 - f0) > epsilon else v0
    if abs(f0 - f1) < epsilon:
        v0, v1 = table_data.get((i0, f0), 0.0), table_data.get((i1, f0), 0.0)
        return v0 + (v1 - v0) * (clamped_induction - i0) / (i1 - i0) if abs(i1 - i0) > epsilon else v0
    q11, q12, q21, q22 = table_data.get((i0, f0), 0.0), table_data.get((i0, f1), 0.0), table_data.get((i1, f0), 0.0), table_data.get((i1, f1), 0.0)
    r1_den = (i1 - i0)
    r1 = ((i1 - clamped_induction) * q11 + (clamped_induction - i0) * q21) / r1_den if r1_den > epsilon else (q11 if clamped_induction <= i0 else q21)
    r2_den = (i1 - i0)
    r2 = ((i1 - clamped_induction) * q12 + (clamped_induction - i0) * q22) / r2_den if r2_den > epsilon else (q12 if clamped_induction <= i0 else q22)
    p_den = (f1 - f0)
    return ((f1 - clamped_frequency) * r1 + (clamped_frequency - f0) * r2) / p_den if p_den > epsilon else (r1 if clamped_frequency <= f0 else r2)


# --- Capacitor Bank Calculation Helpers ---
def generate_q_combinations(num_switches=5):
    q_indices = list(range(1, num_switches + 1))
    combinations = []
    for i in range(1, num_switches + 1):
        combinations.extend(itertools.combinations(q_indices, i))
    return [list(comb) for comb in combinations]

def calculate_q_combination_power(q_combination: List[int]) -> float:
    power_steps = const.Q_SWITCH_POWERS["generic_cp"]
    if not power_steps or len(power_steps) == 0:
        log.error("Perfil Q inválido ou incompleto. Retornando 0.0.")
        return 0.0
    return sum(power_steps[q - 1] for q in q_combination) if q_combination else 0.0

def _parse_capacitor_unit_id(unit_id: str) -> Tuple[int, str, int]:
    try:
        position_x = int(unit_id[2])
        phase = unit_id[3]
        instance_y = int(unit_id[4])
        return position_x, phase, instance_y
    except (ValueError, IndexError):
        log.error(f"Falha ao analisar ID da unidade do capacitor: {unit_id}. Formato inválido.")
        raise ValueError(f"Formato de ID de unidade de capacitor inválido: {unit_id}")

def _get_logical_group_from_unit_id(unit_id: str) -> int:
    try:
        group_y = int(unit_id[-1])
        if group_y in [1, 2]: return group_y
        else: log.warning(f"Grupo lógico inválido {group_y} na unidade {unit_id}. Retornando grupo 0."); return 0
    except (ValueError, IndexError): log.error(f"Erro ao extrair grupo lógico da unidade {unit_id}. Retornando grupo 0."); return 0

def _get_available_series_strings(all_units_for_voltage: List[str], bank_voltage_kv: float, group_filter_nums: List[int]) -> Dict[str, List[List[str]]]:
    units_in_series_needed = const.UNITS_PER_SERIES_STRING_BY_VOLTAGE_KV.get(bank_voltage_kv, 0)
    if units_in_series_needed == 0: return {'A': [], 'B': [], 'C': []}
    units_by_phase_logical_group: Dict[str, Dict[int, List[str]]] = {'A': {}, 'B': {}, 'C': {}}
    temp_units_to_sort: Dict[str, Dict[int, List[Tuple[int, str]]]] = {'A': {}, 'B': {}, 'C': {}}
    for unit_id in all_units_for_voltage:
        try:
            pos_x, phase, instance_y_logical_group = _parse_capacitor_unit_id(unit_id)
            if instance_y_logical_group not in group_filter_nums: continue
            if phase not in temp_units_to_sort: continue
            if instance_y_logical_group not in temp_units_to_sort[phase]:
                temp_units_to_sort[phase][instance_y_logical_group] = []
            temp_units_to_sort[phase][instance_y_logical_group].append((pos_x, unit_id))
        except ValueError: continue
    for phase in ['A', 'B', 'C']:
        if phase not in units_by_phase_logical_group: units_by_phase_logical_group[phase] = {}
        for logical_group, unit_tuples in temp_units_to_sort.get(phase, {}).items():
            unit_tuples.sort(key=lambda item: item[0])
            units_by_phase_logical_group[phase][logical_group] = [unit_id for pos_x, unit_id in unit_tuples]
    all_strings_by_phase: Dict[str, List[List[str]]] = {'A': [], 'B': [], 'C': []}
    for phase in ['A', 'B', 'C']:
        for logical_group in group_filter_nums:
            if logical_group not in units_by_phase_logical_group.get(phase, {}): continue
            available_units_for_lg = units_by_phase_logical_group[phase][logical_group]
            num_possible_strings = len(available_units_for_lg) // units_in_series_needed
            for i in range(num_possible_strings):
                start_idx = i * units_in_series_needed
                end_idx = start_idx + units_in_series_needed
                string_candidate = available_units_for_lg[start_idx:end_idx]
                if len(string_candidate) == units_in_series_needed:
                    all_strings_by_phase[phase].append(string_candidate)
    return all_strings_by_phase

def select_target_bank_voltage_keys(max_test_voltage_kv: float, factor_cap_banc_overvoltage: float = 1.1, circuit_type: str = "Trifásico") -> Tuple[Optional[str], Optional[str]]:
    # Validação de segurança para evitar comparações com None
    if max_test_voltage_kv is None:
        max_test_voltage_kv = 0.0

    if circuit_type.lower() == "trifásico": available_bank_voltages_num = sorted(const.EPS_CAP_BANK_VOLTAGES_KV)
    else: available_bank_voltages_num = sorted([float(v) for v in const.MONOPHASE_VOLTAGES])
    if not available_bank_voltages_num: log.warning(f"Nenhuma tensão de banco de capacitores disponível para {circuit_type}."); return None, None
    target_v_sf_key = None
    for v_bank_num in available_bank_voltages_num:
        if v_bank_num >= max_test_voltage_kv - epsilon: target_v_sf_key = str(v_bank_num); break
    if target_v_sf_key is None and available_bank_voltages_num:
        target_v_sf_key = str(available_bank_voltages_num[-1])
    target_v_cf_key = None
    for v_bank_num in reversed(available_bank_voltages_num):
        if v_bank_num < max_test_voltage_kv - epsilon:
            max_voltage_supported_by_cf = v_bank_num * factor_cap_banc_overvoltage
            if max_test_voltage_kv <= max_voltage_supported_by_cf + epsilon: target_v_cf_key = str(v_bank_num); break
    if target_v_sf_key and target_v_cf_key and target_v_sf_key == target_v_cf_key: target_v_cf_key = None
    return target_v_sf_key, target_v_cf_key

def get_cs_configuration(target_bank_voltage_key: Optional[str], chosen_unit_ids: List[str], circuit_type: str) -> str:
    if target_bank_voltage_key is None or not chosen_unit_ids: return "N/A (V Alvo Inv. ou Sem Unidades)"
    active_cs_switches = set()
    for unit_id in chosen_unit_ids:
        try:
            pos_x, phase, instance_y = _parse_capacitor_unit_id(unit_id)
            active_cs_switches.add(f"CS{pos_x}{phase}{instance_y}")
        except ValueError: log.warning(f"Invalid unit_id encountered: {unit_id}"); continue
    if circuit_type.lower() == "trifásico":
        all_allowed_cs_for_voltage = const.CS_SWITCHES_BY_VOLTAGE_TRI.get(target_bank_voltage_key, [])
        for phase_char in ['A', 'B', 'C']:
            if any(unit_id[3] == phase_char for unit_id in chosen_unit_ids):
                if f"CS{phase_char}" in all_allowed_cs_for_voltage: active_cs_switches.add(f"CS{phase_char}")
                if f"CS7{phase_char}" in all_allowed_cs_for_voltage: active_cs_switches.add(f"CS7{phase_char}")
    else:
        all_allowed_cs_for_voltage = const.CS_SWITCHES_BY_VOLTAGE_MONO.get(target_bank_voltage_key, [])
        active_cs_switches.update(['CS6A', 'CS6B', 'CS6C'])
        for phase_char in ['A', 'B', 'C']:
            if any(unit_id[3] == phase_char for unit_id in chosen_unit_ids):
                if f"CS{phase_char}" in all_allowed_cs_for_voltage: active_cs_switches.add(f"CS{phase_char}")
    return ", ".join(sorted(list(active_cs_switches))) if active_cs_switches else "N/A (Sem CSs Ativas)"


def _get_curated_bank_options(
    all_possible_solutions: List[Dict[str, Any]],
    q_banco_necessaria_nominal: float
) -> List[Dict[str, Any]]:

    if not all_possible_solutions:
        return []

    # 1. Calcular campos auxiliares
    for sol in all_possible_solutions:
        sol['meets_ideal_power_req'] = sol.get('meets_ideal_power_req', sol.get('power_sufficient_ideal', False))
        sol['eps_within_limits'] = sol.get('eps_within_limits', False)
        sol['i_eps_abs'] = abs(sol.get('i_eps', float('inf')))
        sol['q_efetiva'] = sol.get('q_efetiva', 0.0)
        sol['num_units_or_strings'] = sol.get('num_units', sol.get('num_parallel_strings', float('inf')))
        sol['q_comb_list'] = sol.get('q_comb_list', []) 
        sol['num_q_switches'] = len(sol['q_comb_list'])
        sol['is_default'] = False
        sol['is_initial_display_default'] = False

    # 2. Pré-filtragem FOCADA: Para cada Qnominal (arredondada), manter apenas a melhor.
    #    "Melhor" aqui significa:
    #    a) EPS OK e Potência Ideal OK
    #    b) Menor número de chaves Q
    #    c) Menor i_eps_abs
    #    d) Menor num_units_or_strings (como último recurso para desempate se tudo acima for igual)
    
    # Agrupar por Qnominal arredondada
    grouped_by_q_nominal = {}
    for sol in all_possible_solutions:
        q_nom_rounded_key = round(sol['q_nominal_provided'], 3) # Chave de agrupamento
        if q_nom_rounded_key not in grouped_by_q_nominal:
            grouped_by_q_nominal[q_nom_rounded_key] = []
        grouped_by_q_nominal[q_nom_rounded_key].append(sol)

    solutions_after_q_filter = []
    for q_nom_key, group_solutions in grouped_by_q_nominal.items():
        # Para este grupo de soluções com a mesma Qnominal, encontrar a melhor
        group_solutions.sort(key=lambda s: (
            not (s['eps_within_limits'] and s['meets_ideal_power_req']), # Prioridade 1
            not s['eps_within_limits'],                                  # Prioridade 2
            s['num_q_switches'],                                         # Prioridade 3 (MENOS CHAVES)
            s['i_eps_abs'],                                              # Prioridade 4
            s['num_units_or_strings'],                                   # Prioridade 5 (MENOS UNIDADES/STRINGS)
            -s['q_efetiva']                                              # Prioridade 6
        ))
        solutions_after_q_filter.append(group_solutions[0]) # Adicionar apenas a melhor do grupo

    if not solutions_after_q_filter:
        return []
        
    # 3. Ordenar a lista filtrada (solutions_after_q_filter) pela "qualidade geral"
    #    Esta ordenação é para a lógica de curadoria das 5 melhores e para 'is_default'.
    solutions_after_q_filter.sort(key=lambda s: (
        not (s['eps_within_limits'] and s['meets_ideal_power_req']),
        not (s['eps_within_limits'] and s.get('meets_min_power_req', False) and not s['meets_ideal_power_req']),
        not s['meets_ideal_power_req'],
        not s['eps_within_limits'],
        not s.get('meets_min_power_req', False),
        s['i_eps_abs'],
        abs(s['q_nominal_provided'] - q_banco_necessaria_nominal),
        s['num_q_switches'], 
        -s['q_efetiva'],
        s['num_units_or_strings']
    ))
    
    best_overall_solution_candidate = solutions_after_q_filter[0] if solutions_after_q_filter else None
    if not best_overall_solution_candidate: return []

    # 4. Selecionar até 5 opções para display (curadoria em torno da 'best_overall_solution_candidate')
    #    A lista 'solutions_after_q_filter' já está otimizada por chaves Q para cada Qnominal.
    
    # Ordenar por Q_nominal para a lógica de pegar vizinhos
    solutions_q_sorted_from_filtered = sorted(solutions_after_q_filter, key=lambda s: (
        s['q_nominal_provided'], 
        s['num_q_switches'], 
        s['i_eps_abs']
    ))

    central_idx_in_q_sorted = -1
    # Encontrar a 'best_overall_solution_candidate' na lista ordenada por Q_nominal
    for i, sol in enumerate(solutions_q_sorted_from_filtered):
        if sol is best_overall_solution_candidate:
            central_idx_in_q_sorted = i
            break
    if central_idx_in_q_sorted == -1 : central_idx_in_q_sorted = 0 if solutions_q_sorted_from_filtered else -1
    
    curated_options_list = []
    if best_overall_solution_candidate:
        curated_options_list.append(best_overall_solution_candidate)
    
    lower_added = 0
    if central_idx_in_q_sorted != -1:
        for i in range(central_idx_in_q_sorted - 1, -1, -1):
            if len(curated_options_list) >= 5 or lower_added >= 2: break
            sol = solutions_q_sorted_from_filtered[i]
            if sol not in curated_options_list:
                curated_options_list.append(sol); lower_added += 1
        
    higher_added = 0
    if central_idx_in_q_sorted != -1:
        for i in range(central_idx_in_q_sorted + 1, len(solutions_q_sorted_from_filtered)):
            if len(curated_options_list) >= 5 or higher_added >= 2: break
            sol = solutions_q_sorted_from_filtered[i]
            if sol not in curated_options_list:
                curated_options_list.append(sol); higher_added += 1

    idx_fill = 0
    # Preencher com a lista 'solutions_after_q_filter' (já ordenada por "qualidade geral")
    while len(curated_options_list) < 5 and idx_fill < len(solutions_after_q_filter):
        sol_to_add = solutions_after_q_filter[idx_fill]
        if sol_to_add not in curated_options_list:
            curated_options_list.append(sol_to_add)
        idx_fill += 1
    
    final_list_for_display = curated_options_list[:5]

    if not final_list_for_display: return []

    # 5. Marcar 'is_default'
    # 'is_default' é a best_overall_solution_candidate, que é a primeira de 'solutions_after_q_filter'
    for opt in final_list_for_display:
        opt['is_default'] = (opt is best_overall_solution_candidate)


    # 6. Marcar 'is_initial_display_default' (radio button)
    # Ordenar a 'final_list_for_display' (de até 5 itens) por critérios específicos para o radio.
    temp_list_for_radio_selection = list(final_list_for_display) 
    temp_list_for_radio_selection.sort(key=lambda s: (
        not s['eps_within_limits'],       # 1. EPS OK
        s['i_eps_abs'],                   # 2. Menor corrente EPS
        s['num_q_switches'],              # 3. Menos chaves Q
        not s['meets_ideal_power_req'],   # 4. Atende potência ideal
        not s.get('meets_min_power_req', False), # 5. Atende potência mínima
        s['num_units_or_strings'],        # 6. Menos unidades/strings
        -s['q_efetiva'],                  # 7. Maior Q efetiva
        # q_nominal_provided é menos importante aqui, já que o filtro inicial já agrupou por ela
    ))
    
    if temp_list_for_radio_selection:
        solution_for_initial_radio = temp_list_for_radio_selection[0]
        for opt in final_list_for_display: 
            opt['is_initial_display_default'] = (opt is solution_for_initial_radio)
    elif final_list_for_display: 
        final_list_for_display[0]['is_initial_display_default'] = True


    # 7. Ordenação final da lista para exibição na UI (por q_nominal_provided, depois por num_q_switches)
    final_list_for_display.sort(key=lambda s: (s['q_nominal_provided'], s['num_q_switches']))

    return final_list_for_display

def _find_and_rank_q_trifasico(
    all_strings_by_phase: Dict[str, List[List[str]]],
    q_teste_mvar: float, v_teste_kv: float, i_test_a: float,
    v_sut_hv_tap_v_referencia: float,
    target_bank_voltage_kv: float,
    q_combinations_with_power: List[Tuple[float, List[int]]],
    voltage_correction_factor: float,
    tipo_transformador: str = "Trifásico",
    grupo_ligacao: str = ""
) -> List[Dict[str, Any]]:

    sqrt_3_factor = const.SQRT_3

    # Obter limites EPS dinâmicos baseados no tipo de transformador
    transformer_data = {"tipo_transformador": tipo_transformador, "grupo_ligacao": grupo_ligacao}
    eps_limits = const.get_eps_limits(transformer_data)
    eps_current_limit_positive = eps_limits.get("current_limit_positive_a", const.EPS_CURRENT_LIMIT_POSITIVE)
    eps_current_limit_negative = eps_limits.get("current_limit_negative_a", const.EPS_CURRENT_LIMIT_NEGATIVE)

    # Validação de segurança para evitar comparações com None
    if eps_current_limit_positive is None:
        eps_current_limit_positive = const.EPS_CURRENT_LIMIT_POSITIVE
    if eps_current_limit_negative is None:
        eps_current_limit_negative = const.EPS_CURRENT_LIMIT_NEGATIVE

    if voltage_correction_factor <= epsilon:
        return [{'q_config_str': "N/A (V_banco=0)", 'q_nominal_provided': 0.0, 'q_efetiva': 0.0, 'eps_within_limits': False, 'meets_ideal_power_req': False, 'units': [], 'status': 'failure', 'i_eps': None, 'is_default': True, 'is_initial_display_default': True}]

    q_banco_necessaria_nominal = q_teste_mvar / voltage_correction_factor if voltage_correction_factor > epsilon else float('inf')
    
    num_strings_A = len(all_strings_by_phase['A'])
    num_strings_B = len(all_strings_by_phase['B'])
    num_strings_C = len(all_strings_by_phase['C'])
    min_parallel_strings = min(num_strings_A, num_strings_B, num_strings_C)

    if min_parallel_strings == 0:
        return [{'q_config_str': "N/A (Desbalanceado)", 'q_nominal_provided': 0.0, 'q_efetiva': 0.0, 'eps_within_limits': False, 'meets_ideal_power_req': False, 'units': [], 'status': 'failure', 'i_eps': None, 'is_default': True, 'is_initial_display_default': True}]

    all_solutions_collected = []
    for q_nominal_per_unit, q_comb in q_combinations_with_power:
        if not q_comb and q_nominal_per_unit > epsilon: continue
        for num_p_strings in range(1, min_parallel_strings + 1):
            current_units_config: List[str] = []
            for phase in ['A', 'B', 'C']:
                for i in range(num_p_strings):
                    current_units_config.extend(all_strings_by_phase[phase][i])
            num_units_this_config = len(current_units_config)
            if num_units_this_config == 0: continue
            
            q_nominal_provided = q_nominal_per_unit * num_units_this_config

            q_efetiva = q_nominal_provided * voltage_correction_factor
            i_cap = (q_efetiva * 1000) / (v_teste_kv * sqrt_3_factor) if v_teste_kv * sqrt_3_factor > epsilon else 0
            i_eps = (i_test_a - i_cap) * (v_sut_hv_tap_v_referencia / const.SUT_BT_VOLTAGE) if const.SUT_BT_VOLTAGE > epsilon else 0
            is_eps_within_limits = eps_current_limit_negative - epsilon <= i_eps <= eps_current_limit_positive + epsilon
            meets_ideal_power_req = q_efetiva >= q_teste_mvar - epsilon
            meets_min_power_req = q_efetiva >= (q_teste_mvar * 0.90) - epsilon
            q_config_str = ", ".join(f"Q{q}" for q in q_comb) if q_comb else "Nenhuma Chave Q"
            all_solutions_collected.append({
                'q_config_str': q_config_str, 'q_nominal_provided': q_nominal_provided,
                'q_efetiva': q_efetiva, 'eps_within_limits': bool(is_eps_within_limits),
                'meets_ideal_power_req': bool(meets_ideal_power_req),
                'meets_min_power_req': bool(meets_min_power_req),
                'units': current_units_config, 'i_eps': i_eps,
                'num_parallel_strings': num_p_strings,
                'q_comb_list': q_comb
            })

    if all_solutions_collected:
        curated_options = _get_curated_bank_options(all_solutions_collected, q_banco_necessaria_nominal)
        if curated_options: return curated_options
        else: log.warning(f"  TRI: _get_curated_bank_options retornou lista vazia. Usando fallback.")
    
    max_units_config_fallback: List[str] = []
    if min_parallel_strings > 0 :
         for phase_f in ['A', 'B', 'C']:
            for i_f in range(min_parallel_strings):
                max_units_config_fallback.extend(all_strings_by_phase[phase_f][i_f])
    max_q_nominal_per_unit_fallback = calculate_q_combination_power(list(range(1, 6)))
    max_q_nominal_total_fallback = max_q_nominal_per_unit_fallback * len(max_units_config_fallback) if max_units_config_fallback else 0
    max_q_efetiva_total_fallback = max_q_nominal_total_fallback * voltage_correction_factor
    return [{'q_config_str': f"N/A (Q_req > Max {max_q_efetiva_total_fallback:.1f})", 'q_nominal_provided': max_q_nominal_total_fallback, 'q_efetiva': max_q_efetiva_total_fallback, 'eps_within_limits': False, 'meets_ideal_power_req': False, 'units': max_units_config_fallback, 'status': 'failure', 'i_eps': None, 'is_default': True, 'is_initial_display_default': True}]

def _find_and_rank_q_monofasico(
    all_strings_by_phase: Dict[str, List[List[str]]], # Contém listas de strings (cada string é uma lista de unit_ids)
    q_teste_mvar: float, v_teste_kv: float, i_test_a: float,
    v_sut_hv_tap_v_referencia: float,
    target_bank_voltage_kv: float, # Numérico
    q_combinations_with_power: List[Tuple[float, List[int]]],
    voltage_correction_factor: float,
    group_filter_nums: List[int],
    power_sub_compensation_tolerance_factor: float = 0.95,
    tipo_transformador: str = "Monofásico",
    grupo_ligacao: str = ""
) -> List[Dict[str, Any]]:

    # Obter limites EPS dinâmicos baseados no tipo de transformador
    transformer_data = {"tipo_transformador": tipo_transformador, "grupo_ligacao": grupo_ligacao}
    eps_limits = const.get_eps_limits(transformer_data)
    eps_current_limit_positive = eps_limits.get("current_limit_positive_a", const.EPS_CURRENT_LIMIT_POSITIVE)
    eps_current_limit_negative = eps_limits.get("current_limit_negative_a", const.EPS_CURRENT_LIMIT_NEGATIVE)

    # Validação de segurança para evitar comparações com None
    if eps_current_limit_positive is None:
        eps_current_limit_positive = const.EPS_CURRENT_LIMIT_POSITIVE
    if eps_current_limit_negative is None:
        eps_current_limit_negative = const.EPS_CURRENT_LIMIT_NEGATIVE

    target_bank_voltage_key = str(target_bank_voltage_kv)

    all_available_strings_in_group: List[List[str]] = []
    # Mapeamento para rastrear a fase de origem de cada string
    string_origin_phases: Dict[int, str] = {} # index_in_all_available_strings -> phase_char
    temp_idx_counter = 0
    for phase_key in sorted(all_strings_by_phase.keys()): # ['A', 'B', 'C']
        if phase_key in all_strings_by_phase:
            for string_content in all_strings_by_phase[phase_key]:
                all_available_strings_in_group.append(string_content)
                string_origin_phases[temp_idx_counter] = phase_key
                temp_idx_counter += 1


    if not all_available_strings_in_group:
        return [{'q_config_str': "N/A (Sem Strings no Grupo)", 'q_nominal_provided': 0.0, 'q_efetiva': 0.0, 'eps_within_limits': False, 'meets_ideal_power_req': False, 'units': [], 'status': 'failure', 'i_eps': None, 'is_default': True, 'is_initial_display_default': True}]

    units_per_string_serie = const.UNITS_PER_SERIES_STRING_BY_VOLTAGE_KV.get(target_bank_voltage_kv, 1)
    if units_per_string_serie == 0 : units_per_string_serie = 1

    if voltage_correction_factor <= epsilon:
        return [{'q_config_str': "N/A (V_banco=0)", 'q_nominal_provided': 0.0, 'q_efetiva': 0.0, 'eps_within_limits': False, 'meets_ideal_power_req': False, 'units': [], 'status': 'failure', 'i_eps': None, 'is_default': True, 'is_initial_display_default': True}]

    q_banco_necessaria_nominal = q_teste_mvar / voltage_correction_factor if voltage_correction_factor > epsilon else float('inf')
    q_banco_min_nominal_aceitavel = q_banco_necessaria_nominal * power_sub_compensation_tolerance_factor

    max_q_nominal_for_group = float('inf')
    voltage_limits_config = const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.get(target_bank_voltage_key)
    if voltage_limits_config:
        if group_filter_nums == [1]: max_q_nominal_for_group = voltage_limits_config.get("grupo1", {}).get("max", float('inf'))
        elif group_filter_nums == [1, 2]: max_q_nominal_for_group = voltage_limits_config.get("grupo1_2", {}).get("max", float('inf'))

    all_solutions_collected = []
    for q_nominal_per_physical_unit, q_comb in q_combinations_with_power:
        if not q_comb and q_nominal_per_physical_unit > epsilon: continue
        if q_nominal_per_physical_unit <= epsilon and q_teste_mvar > epsilon : continue

        for num_parallel_strings_to_use in range(1, len(all_available_strings_in_group) + 1):
            # active_strings são as strings selecionadas para este banco monofásico
            active_strings_with_indices = [(all_available_strings_in_group[i], i) for i in range(num_parallel_strings_to_use)]
            
            active_units_physical: List[str] = []
            phases_represented_in_bank: Dict[str, int] = {'A': 0, 'B': 0, 'C': 0} # Contar strings por fase de origem

            for string_content, original_string_idx in active_strings_with_indices:
                active_units_physical.extend(string_content)
                origin_phase = string_origin_phases.get(original_string_idx)
                if origin_phase:
                    phases_represented_in_bank[origin_phase] += 1
            
            num_total_physical_units = len(active_units_physical)
            if num_total_physical_units != (num_parallel_strings_to_use * units_per_string_serie) and num_total_physical_units > 0 :
                log.warning(f"MONO: Inconsistência no número de unidades físicas ({num_total_physical_units}) vs. strings*unidades_por_string ({num_parallel_strings_to_use}*{units_per_string_serie}). Usando num_total_physical_units.")
            if num_total_physical_units == 0 and num_parallel_strings_to_use > 0 : continue

            q_nominal_provided = q_nominal_per_physical_unit * num_total_physical_units

            if q_nominal_provided > max_q_nominal_for_group + epsilon:
                break 

            q_efetiva = q_nominal_provided * voltage_correction_factor
            i_cap = (q_efetiva * 1000) / v_teste_kv if v_teste_kv > epsilon else 0
            i_eps_raw = (i_test_a - i_cap) * (v_sut_hv_tap_v_referencia / const.SUT_BT_VOLTAGE) if const.SUT_BT_VOLTAGE > epsilon else 0
            i_eps = i_eps_raw * const.SQRT_3
            is_eps_within_limits = eps_current_limit_negative - epsilon <= i_eps <= eps_current_limit_positive + epsilon
            meets_min_power_req = q_nominal_provided >= q_banco_min_nominal_aceitavel - epsilon
            meets_ideal_power_req = q_nominal_provided >= q_banco_necessaria_nominal - epsilon
            
            # Nova construção da q_config_str para monofásico
            q_keys_str = [f"Q{q}" for q in q_comb] if q_comb else ["Nenhuma"]
            phase_config_parts = []
            for phase_char_cfg, count in phases_represented_in_bank.items():
                if count > 0: # Se esta fase contribui com strings/unidades para o banco monofásico
                    # Para monofásico, todas as unidades ativas compartilham a mesma q_comb
                    phase_config_parts.append(f"{phase_char_cfg}: {', '.join(q_keys_str)}")
            
            if not phase_config_parts and not q_comb : # Caso Q_teste=0, nenhuma unidade, nenhuma chave Q
                q_config_display_str = "Nenhuma Chave Q"
            elif not phase_config_parts and q_comb: # Impossível, mas como fallback
                 q_config_display_str = f"Config Q: {', '.join(q_keys_str)} (Fases não det.)"
            else:
                 q_config_display_str = "; ".join(phase_config_parts)


            all_solutions_collected.append({
                'q_config_str': q_config_display_str, 
                'q_nominal_provided': q_nominal_provided,
                'q_efetiva': q_efetiva, 
                'eps_within_limits': bool(is_eps_within_limits),
                'units': active_units_physical, 
                'i_eps': i_eps, 
                'num_units': num_total_physical_units, 
                'num_parallel_strings': num_parallel_strings_to_use,
                'meets_min_power_req': bool(meets_min_power_req), 
                'meets_ideal_power_req': bool(meets_ideal_power_req),
                'q_comb_list': q_comb
            })

    if not all_solutions_collected:
         q_max_poss_str = f"{max_q_nominal_for_group:.1f}" if max_q_nominal_for_group != float('inf') else "N/D"
         if q_teste_mvar > epsilon and any(qc[0] <= epsilon for qc in q_combinations_with_power):
              return [{'q_config_str': "N/A (Comb. Q s/ potência)", 'status': 'failure', 'q_nominal_provided':0.0, 'q_efetiva':0.0, 'eps_within_limits': False, 'meets_ideal_power_req': False, 'units':[], 'i_eps':None, 'is_default':True, 'is_initial_display_default': True}]
         return [{'q_config_str': f"N/A (Req>Max Grupo {q_max_poss_str}MVAr)", 'status': 'failure', 'q_nominal_provided':0.0, 'q_efetiva':0.0, 'eps_within_limits': False, 'meets_ideal_power_req': False, 'units':[], 'i_eps':None, 'is_default':True, 'is_initial_display_default': True}]

    curated_options = _get_curated_bank_options(all_solutions_collected, q_banco_necessaria_nominal)
    
    if curated_options: 
        return curated_options
    else: 
        log.warning(f"  MONO: _get_curated_bank_options retornou lista vazia mesmo com soluções ({len(all_solutions_collected)}). Usando fallback.")
        max_q_nominal_per_physical_unit_fallback = calculate_q_combination_power(list(range(1,6)))
        
        num_physical_units_for_max_power = 0
        if max_q_nominal_per_physical_unit_fallback > epsilon and max_q_nominal_for_group != float('inf'):
            num_physical_units_for_max_power = int(max_q_nominal_for_group / max_q_nominal_per_physical_unit_fallback)
        
        num_strings_for_max_power = 0
        if units_per_string_serie > 0 and num_physical_units_for_max_power > 0:
             num_strings_for_max_power = num_physical_units_for_max_power // units_per_string_serie
        
        num_strings_to_use_fallback = min(num_strings_for_max_power, len(all_available_strings_in_group))

        final_units_fallback_fb = []
        if num_strings_to_use_fallback > 0:
            temp_active_strings_fb = all_available_strings_in_group[:num_strings_to_use_fallback]
            for s_fb_loop in temp_active_strings_fb:
                final_units_fallback_fb.extend(s_fb_loop)
        
        max_q_nominal_total_fallback = max_q_nominal_per_physical_unit_fallback * len(final_units_fallback_fb)
        max_q_efetiva_possible_fb = max_q_nominal_total_fallback * voltage_correction_factor
        
        # Fallback q_config_str
        active_phases_fb = sorted(list(set(uid[3] for uid in final_units_fallback_fb)))
        q_keys_fb_str = [f"Q{q}" for q in range(1,6)] # Assume all Q for max power
        fb_phase_parts = [f"{p}: {', '.join(q_keys_fb_str)}" for p in active_phases_fb]
        q_config_fb_str = "; ".join(fb_phase_parts) if fb_phase_parts else "N/A (Fallback Max)"


        return [{'q_config_str': f"N/A (Q_req > Max {max_q_efetiva_possible_fb:.1f}, Usando: {q_config_fb_str})", 
                 'q_nominal_provided': max_q_nominal_total_fallback, 
                 'q_efetiva': max_q_efetiva_possible_fb, 'eps_within_limits': False, 
                 'meets_ideal_power_req': False, 'units': final_units_fallback_fb, 
                 'status': 'failure', 'i_eps': None, 'is_default': True, 
                 'is_initial_display_default': True}]
       
def _find_ranked_q_options(
    all_units_for_voltage: List[str],
    q_teste_mvar: float, v_teste_kv: float, i_test_a: float,
    tipo_transformador: str,
    v_sut_hv_tap_v_referencia: float,
    target_bank_voltage_kv: float, # Este já é numérico aqui
    group_filter_nums: List[int],
    grupo_ligacao: str = ""
) -> List[Dict[str, Any]]:

    if q_teste_mvar < 0: q_teste_mvar = 0.0
    if not all_units_for_voltage and q_teste_mvar > epsilon :
        return [{'q_config_str': "N/A (Sem Unidades)", 'status': 'failure', 'units': [], 'q_nominal_provided':0.0, 'q_efetiva':0.0, 'eps_within_limits': False, 'power_sufficient_ideal':False, 'meets_ideal_power_req':False, 'i_eps':None, 'is_default': True, 'is_initial_display_default': True}]

    if q_teste_mvar <= epsilon:
        i_eps_no_bank = i_test_a * (v_sut_hv_tap_v_referencia / const.SUT_BT_VOLTAGE) if const.SUT_BT_VOLTAGE > epsilon else 0
        # Removido fator √3 para monofásico conforme solicitado
        # if normalize_transformer_type(tipo_transformador) == "Monofásico": i_eps_no_bank *= const.SQRT_3

        # Usar limites EPS dinâmicos baseados no tipo de transformador
        transformer_data = {"tipo_transformador": tipo_transformador, "grupo_ligacao": grupo_ligacao}
        eps_limits = const.get_eps_limits(transformer_data)
        eps_current_limit_positive = eps_limits.get("current_limit_positive_a", const.EPS_CURRENT_LIMIT_POSITIVE)
        eps_current_limit_negative = eps_limits.get("current_limit_negative_a", const.EPS_CURRENT_LIMIT_NEGATIVE)

        # Validação de segurança para evitar comparações com None
        if eps_current_limit_positive is None:
            eps_current_limit_positive = const.EPS_CURRENT_LIMIT_POSITIVE
        if eps_current_limit_negative is None:
            eps_current_limit_negative = const.EPS_CURRENT_LIMIT_NEGATIVE

        eps_ok_no_bank = eps_current_limit_negative - epsilon <= i_eps_no_bank <= eps_current_limit_positive + epsilon
        return [{'q_config_str': "N/A (Q_teste≈0)", 'q_nominal_provided': 0.0, 'q_efetiva': 0.0, 'eps_within_limits': bool(eps_ok_no_bank), 'power_sufficient_ideal': True, 'meets_ideal_power_req':True, 'units': [], 'status': 'ok', 'i_eps':i_eps_no_bank, 'is_default':True, 'is_initial_display_default': True }]

    voltage_correction_factor = (v_teste_kv / target_bank_voltage_kv)**2 if target_bank_voltage_kv > epsilon else 0.0
    all_strings_by_phase = _get_available_series_strings(all_units_for_voltage, target_bank_voltage_kv, group_filter_nums)

    num_q_stages = len(const.Q_SWITCH_POWERS["generic_cp"])
    q_combinations = [[]] + generate_q_combinations(num_q_stages)
    q_combinations_with_power = []
    for q_comb in q_combinations:
        q_combinations_with_power.append((calculate_q_combination_power(q_comb), q_comb))
    q_combinations_with_power.sort(key=lambda x: (x[0], len(x[1])))

    if normalize_transformer_type(tipo_transformador) == "Trifásico":
        # _find_and_rank_q_trifasico não precisa de group_filter_nums diretamente,
        # pois all_strings_by_phase já foi filtrado por _get_available_series_strings usando group_filter_nums
        return _find_and_rank_q_trifasico(
            all_strings_by_phase, q_teste_mvar, v_teste_kv, i_test_a,
            v_sut_hv_tap_v_referencia, target_bank_voltage_kv, q_combinations_with_power, voltage_correction_factor,
            tipo_transformador, grupo_ligacao
        )
    else: # Monofásico
        return _find_and_rank_q_monofasico(
            all_strings_by_phase, q_teste_mvar, v_teste_kv, i_test_a,
            v_sut_hv_tap_v_referencia, target_bank_voltage_kv,
            q_combinations_with_power, voltage_correction_factor,
            group_filter_nums=group_filter_nums,  # Passando o group_filter_nums
            power_sub_compensation_tolerance_factor=0.95,
            tipo_transformador=tipo_transformador,
            grupo_ligacao=grupo_ligacao
        )

def _select_group_and_get_ranked_options(
    target_bank_voltage_key: Optional[str],
    q_teste_mvar: float, v_teste_kv: float, i_test_a: float,
    tipo_transformador: str,
    v_sut_hv_tap_v_referencia: float,
    grupo_ligacao: str = ""
) -> Tuple[str, List[Dict[str, Any]]]:

    if q_teste_mvar < 0: q_teste_mvar = 0.0
    if target_bank_voltage_key is None or q_teste_mvar <= epsilon:
        i_eps_no_bank = i_test_a * (v_sut_hv_tap_v_referencia / const.SUT_BT_VOLTAGE) if const.SUT_BT_VOLTAGE > epsilon else 0
        # Removido fator √3 para monofásico conforme solicitado
        # if normalize_transformer_type(tipo_transformador) == "Monofásico": i_eps_no_bank *= const.SQRT_3

        # Usar limites EPS dinâmicos baseados no tipo de transformador
        transformer_data = {"tipo_transformador": tipo_transformador, "grupo_ligacao": grupo_ligacao}
        eps_limits = const.get_eps_limits(transformer_data)
        eps_current_limit_positive = eps_limits.get("current_limit_positive_a", const.EPS_CURRENT_LIMIT_POSITIVE)
        eps_current_limit_negative = eps_limits.get("current_limit_negative_a", const.EPS_CURRENT_LIMIT_NEGATIVE)

        # Validação de segurança para evitar comparações com None
        if eps_current_limit_positive is None:
            eps_current_limit_positive = const.EPS_CURRENT_LIMIT_POSITIVE
        if eps_current_limit_negative is None:
            eps_current_limit_negative = const.EPS_CURRENT_LIMIT_NEGATIVE

        eps_ok_no_bank = eps_current_limit_negative - epsilon <= i_eps_no_bank <= eps_current_limit_positive + epsilon
        solution = {'q_config_str': "N/A (Q_teste≈0)", 'q_nominal_provided': 0.0, 'q_efetiva': 0.0, 'eps_within_limits': bool(eps_ok_no_bank), 'power_sufficient_ideal': True, 'meets_ideal_power_req': True, 'units': [], 'status': 'ok', 'i_eps':i_eps_no_bank, 'is_default':True, 'is_initial_display_default': True}
        return "Não Req.", [solution]


    target_bank_voltage_kv_num = float(target_bank_voltage_key)

    if normalize_transformer_type(tipo_transformador) == "Trifásico":
        all_caps_for_voltage = const.CAPACITORS_BY_VOLTAGE_TRI.get(target_bank_voltage_key, [])
    else: # Monofásico
        all_caps_for_voltage = const.CAPACITORS_BY_VOLTAGE_MONO.get(
            target_bank_voltage_key, 
            const.CAPACITORS_BY_VOLTAGE_TRI.get(target_bank_voltage_key, [])
        )


    if not all_caps_for_voltage:
        return "Erro Caps", [{'q_config_str': f"N/A (Sem Caps p/ {target_bank_voltage_key}kV)", 'status': 'failure', 'units': [], 'q_nominal_provided':0.0, 'q_efetiva':0.0, 'eps_within_limits': False, 'power_sufficient_ideal':False, 'meets_ideal_power_req':False, 'i_eps':None, 'is_default':True, 'is_initial_display_default': True}]


    solutions_g1 = _find_ranked_q_options(
        all_caps_for_voltage, q_teste_mvar, v_teste_kv, i_test_a, tipo_transformador,
        v_sut_hv_tap_v_referencia,
        target_bank_voltage_kv_num, group_filter_nums=[1],
        grupo_ligacao=grupo_ligacao
    )

    g1_is_adequate = False
    _q_banco_nominal_necessaria_log = 0.0
    _grupo1_max_nominal_log = 0.0
    _power_ideal_met_by_best_g1_sol_q_efetiva_log = False
    _eps_ok_g1_log = False

    # Se Q_teste é muito pequeno (≈0), não precisa de banco capacitivo, usar G1 por padrão
    if q_teste_mvar <= epsilon:
        log.info(f"  [select_group] Q_teste muito pequeno ({q_teste_mvar:.3f} MVAr ≤ {epsilon}). Usando Grupo 1 por padrão.")
        return "Grupo 1", solutions_g1 if solutions_g1 else [{'q_config_str': 'N/A (Q_teste≈0)', 'status': 'success', 'units': [], 'q_nominal_provided': 0.0, 'q_efetiva': 0.0, 'eps_within_limits': True, 'meets_ideal_power_req': True, 'i_eps': 0.0, 'is_default': True}]


    if solutions_g1 and solutions_g1[0].get('status') != 'failure':
        best_g1_sol = next((s for s in solutions_g1 if s.get('is_default')), solutions_g1[0])

        _power_ideal_met_by_best_g1_sol_q_efetiva_log = best_g1_sol.get('meets_ideal_power_req', False)
        _eps_ok_g1_log = best_g1_sol.get('eps_within_limits', False) 
        _q_banco_nominal_necessaria_log = q_teste_mvar * (target_bank_voltage_kv_num / v_teste_kv)**2 if v_teste_kv > epsilon else float('inf')
        
        voltage_limits = const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.get(target_bank_voltage_key) 
        _grupo1_max_nominal_log = voltage_limits["grupo1"]["max"] if voltage_limits and "grupo1" in voltage_limits else float('inf')


        if normalize_transformer_type(tipo_transformador) == "Trifásico":
            # Para trifásico, se EPS está OK e a potência necessária está dentro do limite do G1, usar G1
            if _eps_ok_g1_log and (_q_banco_nominal_necessaria_log <= _grupo1_max_nominal_log + epsilon):
                # Se a potência ideal é atendida, definitivamente usar G1
                if _power_ideal_met_by_best_g1_sol_q_efetiva_log:
                    g1_is_adequate = True
                # Se não atende a potência ideal mas tem Q efetiva e está dentro dos limites, ainda usar G1
                elif best_g1_sol.get('q_efetiva', 0.0) > epsilon:
                    g1_is_adequate = True
                # Se Q_teste é muito pequeno (≈0), não precisa de banco, usar G1 por padrão
                elif q_teste_mvar <= epsilon:
                    g1_is_adequate = True
        else: # Monofásico
            if _power_ideal_met_by_best_g1_sol_q_efetiva_log and _eps_ok_g1_log:
                g1_is_adequate = True
            elif not _power_ideal_met_by_best_g1_sol_q_efetiva_log and _eps_ok_g1_log and \
                 best_g1_sol.get('q_efetiva', 0.0) > epsilon and \
                 (_q_banco_nominal_necessaria_log <= _grupo1_max_nominal_log + epsilon): 
                 g1_is_adequate = True
    
    log_g1_decision_details = (
        f"Qnom_nec={_q_banco_nominal_necessaria_log:.2f}, "
        f"G1_max={_grupo1_max_nominal_log:.2f}, "
        f"BestG1_Qef_ideal_met={_power_ideal_met_by_best_g1_sol_q_efetiva_log}, "
        f"EPS_G1_OK={_eps_ok_g1_log}, "
        f"BestG1_Qef={best_g1_sol.get('q_efetiva', 0.0):.2f}, "
        f"Tipo={tipo_transformador}, "
        f"g1_is_adequate={g1_is_adequate}"
    )

    if g1_is_adequate:
        log.info(f"  [select_group] Solução com Grupo 1 escolhida. Detalhes: {log_g1_decision_details}")
        return "Grupo 1", solutions_g1
    else: 
        log.warning(f"  [select_group] Opção com Grupo 1 não adequada/possível. Detalhes: {log_g1_decision_details}. Escalando para Grupo 1+2...")
        solutions_g12 = _find_ranked_q_options(
            all_caps_for_voltage, q_teste_mvar, v_teste_kv, i_test_a, tipo_transformador,
            v_sut_hv_tap_v_referencia,
            target_bank_voltage_kv_num, group_filter_nums=[1, 2],
            grupo_ligacao=grupo_ligacao
        )

        if not solutions_g12 or solutions_g12[0].get('status') == 'failure':
             log.error(f"  [select_group] Nenhuma solução adequada mesmo com Grupo 1+2. (G1+2: {solutions_g12[0]['q_config_str'] if solutions_g12 else 'Falha'})")
             return "Grupo 1+2 (Falha)", solutions_g12 if (solutions_g12 and solutions_g12[0].get('status') == 'failure') else solutions_g1

        log.info(f"  [select_group] Solução com Grupo 1+2 escolhida. (Primeira opção: {solutions_g12[0]['q_config_str']})")
        return "Grupo 1+2", solutions_g12


# --- _build_cap_bank_output_structure ---
def _build_cap_bank_output_structure(
    target_v_key: Optional[str],
    group_name: str,
    solutions_list: List[Dict[str, Any]],
    tipo_transformador: str
) -> Dict[str, Any]:

    if not solutions_list or solutions_list[0].get('status') == 'failure':
        default_sol_data = solutions_list[0] if (solutions_list and solutions_list[0]) else {}
        q_config_str = default_sol_data.get('q_config_str', "N/A (Falha Genérica)")
        if "N/A (Q_req > Max" in q_config_str: q_config_str = "N/A (Potência Insuf.)"
        elif not solutions_list: q_config_str = "N/A (Sem Soluções)"

        fallback_entry = {
            "is_default": True, 
            "is_initial_display_default": True, 
            "cs_config": "N/A", "q_config": q_config_str,
            "q_provided_mvar": default_sol_data.get('q_nominal_provided', 0.0),
            "q_efetiva_banco_mvar": default_sol_data.get('q_efetiva', 0.0),
            "eps_within_limits_estimation": default_sol_data.get('eps_within_limits', False),
            "power_ideal_met": False, "meets_ideal_power_req": False, 
            "group_info": group_name, "i_eps_estimation": default_sol_data.get('i_eps'),
            "units": default_sol_data.get('units', [])
        }
        return {
            "tensao_disp_kv": float(target_v_key) if target_v_key else None,
            "group_info": group_name,
            "cs_config": fallback_entry["cs_config"],
            "q_config": fallback_entry["q_config"],
            "q_provided_mvar": fallback_entry["q_provided_mvar"],
            "q_efetiva_banco_mvar": fallback_entry["q_efetiva_banco_mvar"],
            "eps_within_limits": fallback_entry["eps_within_limits_estimation"],
            "power_ideal_met": fallback_entry["power_ideal_met"], 
            "available_configurations": [fallback_entry]
        }
    
    initial_display_solution = next((s for s in solutions_list if s.get('is_initial_display_default')), solutions_list[0])

    output = {
        "tensao_disp_kv": float(target_v_key) if target_v_key else None,
        "group_info": group_name,
        "cs_config": get_cs_configuration(target_v_key, initial_display_solution.get('units', []), tipo_transformador),
        "q_config": initial_display_solution['q_config_str'],
        "q_provided_mvar": initial_display_solution['q_nominal_provided'],
        "q_efetiva_banco_mvar": initial_display_solution['q_efetiva'],
        "eps_within_limits": initial_display_solution['eps_within_limits'], 
        "power_ideal_met": initial_display_solution.get('meets_ideal_power_req', False), 
        "available_configurations": []
    }

    for solution_item in solutions_list:
        output["available_configurations"].append({
            "is_default": solution_item.get('is_default', False), 
            "is_initial_display_default": solution_item.get('is_initial_display_default', False), 
            "cs_config": get_cs_configuration(target_v_key, solution_item.get('units', []), tipo_transformador),
            "q_config": solution_item['q_config_str'],
            "q_provided_mvar": solution_item['q_nominal_provided'],
            "q_efetiva_banco_mvar": solution_item['q_efetiva'],
            "eps_within_limits_estimation": solution_item['eps_within_limits'],
            "power_ideal_met": solution_item.get('meets_ideal_power_req', False),
            "meets_ideal_power_req": solution_item.get('meets_ideal_power_req', False), 
            "group_info": group_name, 
            "i_eps_estimation": solution_item.get('i_eps'),
            "units": solution_item.get('units', [])
        })
    return output

# --- calculate_no_load_losses ---
def calculate_no_load_losses(data_in: Dict[str, Any]) -> Dict[str, Any]:
    try: data = NoLoadLossesInput(**data_in)
    except Exception as e: log.error(f"Erro de validação para NoLoadLossesInput: {e}"); raise ValueError(f"Dados de perdas em vazio inválidos: {e}")
    
    sqrt_3_factor = const.SQRT_3 if normalize_transformer_type(data.tipo_transformador) == "Trifásico" else 1.0
    # Validação de segurança para evitar comparações com None
    peso_nucleo_ui_safe = data.peso_nucleo_ui if data.peso_nucleo_ui is not None else 0.0
    peso_nucleo_kg_ui = peso_nucleo_ui_safe * 1000
    
    # Validação de segurança para evitar comparações com None
    inducao_ui_safe = data.inducao_ui if data.inducao_ui is not None else 0.0
    frequencia_safe = data.frequencia if data.frequencia is not None else 60.0
    steel_type_safe = data.steel_type if data.steel_type is not None else "M4"

    fator_perdas_aco = interpolate_losses_table_data("perdas_nucleo", inducao_ui_safe, frequencia_safe, steel_type_safe)
    fator_potencia_mag_aco = interpolate_losses_table_data("potencia_magnet", inducao_ui_safe, frequencia_safe, steel_type_safe)
    
    if steel_type_safe == "H110-27":
        fator_perdas_aco *= const.FATOR_CONSTRUCAO_PERDAS_H110_27
        fator_potencia_mag_aco *= const.FATOR_CONSTRUCAO_POTENCIA_MAG_H110_27
    
    if fator_perdas_aco <= epsilon: raise ValueError("Fator de perdas do aço calculado é zero ou negativo, impossível calcular peso do núcleo.")
    
    # Validação de segurança para evitar comparações com None
    perdas_vazio_ui_safe = data.perdas_vazio_ui if data.perdas_vazio_ui is not None else 0.0
    peso_nucleo_calc_kg = (perdas_vazio_ui_safe * 1000) / fator_perdas_aco
    peso_nucleo_calc_ton = peso_nucleo_calc_kg / 1000
    potencia_mag_aco_kvar = (fator_potencia_mag_aco * peso_nucleo_calc_kg) / 1000
    
    # Validação de segurança para evitar comparações com None
    tensao_bt_safe = data.tensao_bt_kv if data.tensao_bt_kv is not None else 0.0
    den_corr_exc_calc = tensao_bt_safe * sqrt_3_factor
    corrente_excitacao_calc_a = potencia_mag_aco_kvar / den_corr_exc_calc if den_corr_exc_calc > epsilon else 0.0
    # Validação de segurança para evitar comparações com None
    corrente_nominal_bt_safe = data.corrente_nominal_bt if data.corrente_nominal_bt is not None else 0.0
    corrente_excitacao_percentual_calc = (corrente_excitacao_calc_a / corrente_nominal_bt_safe) * 100 if corrente_nominal_bt_safe > epsilon else 0.0

    # TODO: Implement further analysis of excitation current, considering harmonics and other factors.
    
    corrente_excitacao_1_1_calc_a = 2 * corrente_excitacao_calc_a 
    corrente_excitacao_1_2_calc_a = 4 * corrente_excitacao_calc_a 

    usar_terciario_para_todos, status_geral_terciario, fonte_tensao = select_optimal_dut_winding_for_test(data)

    if usar_terciario_para_todos:
        tensao_base_kv = data.tensao_terciario_kv if data.tensao_terciario_kv is not None else data.tensao_bt_kv
        corrente_base_a = data.corrente_nominal_terciario if data.corrente_nominal_terciario is not None else data.corrente_nominal_bt
    else:
        tensao_base_kv, corrente_base_a = data.tensao_bt_kv, data.corrente_nominal_bt

    # Validação de segurança para evitar comparações com None
    if corrente_base_a is None:
        corrente_base_a = 0.0
    if tensao_base_kv is None:
        tensao_base_kv = 0.0
    
    tensao_teste_1_0_kv, tensao_teste_1_1_kv, tensao_teste_1_2_kv = tensao_base_kv, tensao_base_kv * 1.1, tensao_base_kv * 1.2
    # Validação de segurança para evitar comparações com None
    corrente_excitacao_ui_safe = data.corrente_excitacao_ui if data.corrente_excitacao_ui is not None else 0.0
    corrente_excitacao_projeto_a = corrente_base_a * (corrente_excitacao_ui_safe / 100.0)
    
    fator_exc_default = const.FATOR_EXCITACAO_DEFAULT_TRIFASICO if normalize_transformer_type(data.tipo_transformador) == "Trifásico" else const.FATOR_EXCITACAO_DEFAULT_MONOFASICO
    
    # Validação de segurança para evitar comparações com None
    corrente_exc_1_1_ui_safe = data.corrente_exc_1_1_ui if data.corrente_exc_1_1_ui is not None else 0.0
    corrente_exc_1_1_proj_a = corrente_base_a * (corrente_exc_1_1_ui_safe / 100.0) if data.corrente_exc_1_1_ui is not None else (fator_exc_default * corrente_excitacao_projeto_a)
    
    if data.corrente_exc_1_2_ui is not None:
        # Validação de segurança para evitar comparações com None
        corrente_exc_1_2_ui_safe = data.corrente_exc_1_2_ui if data.corrente_exc_1_2_ui is not None else 0.0
        corrente_exc_1_2_proj_a = corrente_base_a * (corrente_exc_1_2_ui_safe / 100.0)
    elif data.corrente_exc_1_1_ui is None: 
        corrente_exc_1_2_proj_a = 2 * corrente_exc_1_1_proj_a 
    else: 
        corrente_exc_1_2_proj_a = None

    fator_perdas_projeto_w_kg = (perdas_vazio_ui_safe * 1000) / peso_nucleo_kg_ui if peso_nucleo_kg_ui > epsilon else 0.0
    
    potencia_ensaio_1pu_projeto_kva = tensao_teste_1_0_kv * corrente_excitacao_projeto_a * sqrt_3_factor
    potencia_mag_projeto_kvar = potencia_ensaio_1pu_projeto_kva
    fator_pot_mag_projeto_var_kg = (potencia_mag_projeto_kvar * 1000) / peso_nucleo_kg_ui if peso_nucleo_kg_ui > epsilon else 0.0
    
    potencia_ensaio_1_1pu_projeto_kva = tensao_teste_1_1_kv * corrente_exc_1_1_proj_a * sqrt_3_factor
    potencia_ensaio_1_2pu_projeto_kva = (tensao_teste_1_2_kv * corrente_exc_1_2_proj_a * sqrt_3_factor) if corrente_exc_1_2_proj_a is not None else None
    
    potencia_ensaio_1pu_aco_kva = tensao_teste_1_0_kv * corrente_excitacao_calc_a * sqrt_3_factor
    potencia_ensaio_1_1pu_aco_kva = tensao_teste_1_1_kv * corrente_excitacao_1_1_calc_a * sqrt_3_factor
    potencia_ensaio_1_2pu_aco_kva = tensao_teste_1_2_kv * corrente_excitacao_1_2_calc_a * sqrt_3_factor
    
    validacao_eps_power = {"tem_impedimento": False, "niveis_criticos": [], "alertas_por_nivel": {}}
    validacao_ct_current = {"tem_impedimento": False, "niveis_criticos": [], "alertas_por_nivel": {}}
    validacao_sut_voltage = {"tem_impedimento": False, "niveis_criticos": [], "alertas_por_nivel": {}}

    # Obter limites EPS dinâmicos baseados no tipo de transformador
    transformer_data = get_transformer_data_for_limits(data)
    eps_limits = const.get_eps_limits(transformer_data)
    eps_apparent_power_limit = eps_limits.get("apparent_power_kva", 1450.0)  # Fallback para Trifásico

    # Validação de segurança para evitar comparações com None
    if eps_apparent_power_limit is None:
        eps_apparent_power_limit = 1450.0

    for pu_level, voltage_kv, current_a in [
        ("1.0pu", tensao_teste_1_0_kv, corrente_excitacao_projeto_a),
        ("1.1pu", tensao_teste_1_1_kv, corrente_exc_1_1_proj_a),
        ("1.2pu", tensao_teste_1_2_kv, corrente_exc_1_2_proj_a if corrente_exc_1_2_proj_a is not None else None)
    ]:
        if voltage_kv and current_a is not None:
            # Validação de potência EPS
            potencia_ensaio_kva = voltage_kv * current_a * sqrt_3_factor
            excede_limite_np = potencia_ensaio_kva > eps_apparent_power_limit + epsilon
            excede_limite = bool(excede_limite_np)
            if excede_limite:
                validacao_eps_power["tem_impedimento"] = True
                validacao_eps_power["niveis_criticos"].append(pu_level)

            # Validação de corrente CT
            ct_excede_limite = current_a > const.CT_CURRENT_LIMIT + epsilon
            if ct_excede_limite:
                validacao_ct_current["tem_impedimento"] = True
                validacao_ct_current["niveis_criticos"].append(pu_level)

            # Validação de tensão SUT com limite dinâmico
            transformer_data_for_sut = get_transformer_data_for_limits(data)
            sut_excede_limite, sut_status_msg = check_sut_voltage_limit(voltage_kv, transformer_data_for_sut)
            if sut_excede_limite:
                validacao_sut_voltage["tem_impedimento"] = True
                validacao_sut_voltage["niveis_criticos"].append(pu_level)

            validacao_eps_power["alertas_por_nivel"][pu_level] = {
                "potencia_ensaio_kva": potencia_ensaio_kva,
                "excede_limite": excede_limite
            }

            validacao_ct_current["alertas_por_nivel"][pu_level] = {
                "corrente_teste_a": current_a,
                "excede_limite_ct": ct_excede_limite
            }

            # Obter limite SUT dinâmico para exibição
            transformer_data_for_sut = get_transformer_data_for_limits(data)
            sut_eps_type = const.determine_sut_eps_type(transformer_data_for_sut)
            if sut_eps_type == "Monofásico":
                sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000 / const.SQRT_3
            else:
                sut_max_voltage_kv = const.SUT_AT_MAX_VOLTAGE / 1000

            validacao_sut_voltage["alertas_por_nivel"][pu_level] = {
                "tensao_teste_kv": voltage_kv,
                "excede_limite_sut": sut_excede_limite,
                "sut_max_voltage_kv": sut_max_voltage_kv,
                "sut_eps_type": sut_eps_type
            }

    sut_eps_analysis_results = {}
    scenarios_no_load = {
        "1.0pu": {"Vtest_kv": tensao_teste_1_0_kv, "Itest_a": corrente_excitacao_projeto_a},
        "1.1pu": {"Vtest_kv": tensao_teste_1_1_kv, "Itest_a": corrente_exc_1_1_proj_a}
    }
    if corrente_exc_1_2_proj_a is not None:
        scenarios_no_load["1.2pu"] = {"Vtest_kv": tensao_teste_1_2_kv, "Itest_a": corrente_exc_1_2_proj_a}
    
    for scen_key, scen_params in scenarios_no_load.items():
        V_teste_dut_lv_kv, I_exc_dut_lv_a = scen_params["Vtest_kv"], scen_params["Itest_a"]
        if V_teste_dut_lv_kv is None or I_exc_dut_lv_a is None or V_teste_dut_lv_kv <= epsilon or I_exc_dut_lv_a <= epsilon:
            sut_eps_analysis_results[scen_key] = {"status": "Dados de ensaio insuficientes ou zero.", "taps_info": []}; continue
        
        V_sut_hv_tap_v_referencia = V_teste_dut_lv_kv * 1000

        # Determinar qual saída do SUT usar baseado na tensão de teste e configuração do DUT
        transformer_data = get_transformer_data_for_limits(data)
        sut_source_type, available_sut_voltages_v, sut_display_msg = select_optimal_sut_source_for_dut(V_teste_dut_lv_kv, transformer_data)
        taps_sut_hv_v = available_sut_voltages_v

        log.info(f"[No-Load] Cenário {scen_key}: {sut_display_msg}")

        if not taps_sut_hv_v:
            sut_eps_analysis_results[scen_key] = {"status": f"Faixa SUT {sut_source_type} inválida ou vazia.", "taps_info": []}; continue
        
        # Selecionar tensão ótima do SUT
        ideal_tap_v = select_optimal_sut_voltage(V_teste_dut_lv_kv, taps_sut_hv_v)

        # Configurar taps para display baseado no tipo de saída do SUT
        display_taps_v = []
        if sut_source_type == "SUT_TERC":
            # Para terciário, mostrar apenas as tensões disponíveis (3.5kV, 7kV, 14kV)
            display_taps_v = taps_sut_hv_v
            log.info(f"[No-Load] {scen_key}: Usando terciário do SUT - tensões disponíveis: {[v/1000 for v in taps_sut_hv_v]}kV")
        else:
            # Para AT, usar lógica existente de seleção de taps próximos
            diffs = {tap_v: abs(tap_v - V_sut_hv_tap_v_referencia) for tap_v in taps_sut_hv_v}
            taps_ordenados_por_proximidade = sorted(taps_sut_hv_v, key=lambda tap_v: diffs[tap_v])

            if ideal_tap_v:
                try:
                    ideal_idx = taps_sut_hv_v.index(ideal_tap_v)
                    start_idx = max(0, ideal_idx - 2)
                    end_idx = min(len(taps_sut_hv_v), ideal_idx + 3)
                    display_taps_v = taps_sut_hv_v[start_idx:end_idx]
                except ValueError:
                    display_taps_v = taps_ordenados_por_proximidade[:5]
            else:
                display_taps_v = taps_ordenados_por_proximidade[:5]

            log.info(f"[No-Load] {scen_key}: Usando AT do SUT - tap ideal: {ideal_tap_v/1000:.1f}kV")

        taps_info_list = []
        for V_sut_hv_tap_v_selected in display_taps_v:
            if const.SUT_BT_VOLTAGE <= epsilon: continue
            
            ratio_sut = V_sut_hv_tap_v_selected / const.SUT_BT_VOLTAGE
            I_sut_lv_a = I_exc_dut_lv_a * ratio_sut

            # Removido fator √3 para monofásico conforme solicitado
            # if normalize_transformer_type(data.tipo_transformador) == "Monofásico":
            #     I_sut_lv_a = I_sut_lv_a * const.SQRT_3

            # Usar limites EPS dinâmicos
            eps_current_limit_positive = eps_limits.get("current_limit_positive_a", const.EPS_CURRENT_LIMIT_POSITIVE)
            eps_current_limit_negative = eps_limits.get("current_limit_negative_a", const.EPS_CURRENT_LIMIT_NEGATIVE)

            # Validação de segurança para evitar comparações com None
            if eps_current_limit_positive is None:
                eps_current_limit_positive = const.EPS_CURRENT_LIMIT_POSITIVE
            if eps_current_limit_negative is None:
                eps_current_limit_negative = const.EPS_CURRENT_LIMIT_NEGATIVE

            is_over_positive_np = I_sut_lv_a > eps_current_limit_positive + epsilon
            is_under_negative_np = I_sut_lv_a < eps_current_limit_negative - epsilon
            percent_limite_eps = (I_sut_lv_a / eps_current_limit_positive) * 100 if eps_current_limit_positive > epsilon else float('inf')

            taps_info_list.append({
                "sut_tap_kv": V_sut_hv_tap_v_selected / 1000,
                "corrente_eps_a": I_sut_lv_a,
                "percent_limite_eps": percent_limite_eps,
                "is_over_positive": bool(is_over_positive_np),
                "is_under_negative": bool(is_under_negative_np),
                "eps_current_limit_positive": eps_current_limit_positive,
                "eps_current_limit_negative": eps_current_limit_negative,
                "sut_source_type": sut_source_type,  # Indica se é SUT_TERC ou SUT_AT
                "is_ideal_tap": abs(V_sut_hv_tap_v_selected - ideal_tap_v) < epsilon
            })
            taps_info_list.sort(key=lambda x: x["sut_tap_kv"])

            graph_data = {
                "sut_tap_kv": [tap["sut_tap_kv"] for tap in taps_info_list],
                "corrente_eps_a": [tap["corrente_eps_a"] for tap in taps_info_list],
                "eps_current_limit_positive": eps_current_limit_positive,
                "eps_current_limit_negative": eps_current_limit_negative
            }
            sut_eps_analysis_results[scen_key] = {
                "status": "OK",
                "taps_info": taps_info_list,
                "graph_data": graph_data,
                "sut_source_type": sut_source_type,
                "sut_display_msg": sut_display_msg,
                "ideal_tap_kv": ideal_tap_v / 1000 if ideal_tap_v else None
            }

    potencia_ativa_aco_1_0_kw = (peso_nucleo_calc_kg * fator_perdas_aco) / 1000
    potencia_ativa_aco_1_1_kw = potencia_ativa_aco_1_0_kw * (1.1**2)
    potencia_ativa_aco_1_2_kw = potencia_ativa_aco_1_0_kw * (1.2**2)

    return {
        "parametros_gerais_comparativo": {
            "tensao_nominal_bt_kv": tensao_base_kv, "corrente_nominal_bt_a": corrente_base_a,
            "frequencia_hz": frequencia_safe, "potencia_mag_kvar_projeto": potencia_mag_projeto_kvar,
            "potencia_mag_kvar_aco_m4": potencia_mag_aco_kvar,
            "fator_perdas_mag_kvar_kg_projeto": fator_pot_mag_projeto_var_kg,
            "fator_perdas_mag_var_kg_aco_m4": fator_potencia_mag_aco,
            "fator_perdas_kw_kg_projeto": fator_perdas_projeto_w_kg,
            "fator_perdas_kw_kg_aco_m4": fator_perdas_aco,
            "peso_nucleo_ton_projeto": data.peso_nucleo_ui, "peso_nucleo_ton_aco_m4": peso_nucleo_calc_ton,
            "corrente_excitacao_perc_projeto": corrente_excitacao_ui_safe,
            "corrente_excitacao_perc_aco_m4": corrente_excitacao_percentual_calc,
            "perdas_vazio_kw_usada_calculo": perdas_vazio_ui_safe,
            "fonte_tensao_usada": fonte_tensao, "status_terciario": status_geral_terciario
        },
        "resultados_dut_niveis_tensao": {
            "projeto": {
                "tensao_1_0_pu_kv": tensao_teste_1_0_kv, "corrente_1_0_pu_a": corrente_excitacao_projeto_a,
                "corrente_1_0_pu_perc_nominal_bt": (corrente_excitacao_projeto_a / corrente_base_a) * 100 if corrente_base_a > epsilon else 0,
                "potencia_1_0_pu_kva": potencia_ensaio_1pu_projeto_kva, "potencia_ativa_1_0_pu_kw": perdas_vazio_ui_safe,
                "tensao_1_1_pu_kv": tensao_teste_1_1_kv, "corrente_1_1_pu_a": corrente_exc_1_1_proj_a,
                "corrente_1_1_pu_perc_nominal_bt": (corrente_exc_1_1_proj_a / corrente_base_a) * 100 if corrente_base_a > epsilon else 0,
                "potencia_1_1_pu_kva": potencia_ensaio_1_1pu_projeto_kva, "potencia_ativa_1_1_pu_kw": perdas_vazio_ui_safe * (1.1**2),
                "tensao_1_2_pu_kv": tensao_teste_1_2_kv if corrente_exc_1_2_proj_a is not None else None,
                "corrente_1_2_pu_a": corrente_exc_1_2_proj_a,
                "corrente_1_2_pu_perc_nominal_bt": (corrente_exc_1_2_proj_a / corrente_base_a) * 100 if corrente_exc_1_2_proj_a is not None and corrente_base_a > epsilon else (0 if corrente_exc_1_2_proj_a is not None else None),
                "potencia_1_2_pu_kva": potencia_ensaio_1_2pu_projeto_kva,
                "potencia_ativa_1_2_pu_kw": perdas_vazio_ui_safe * (1.2**2) if corrente_exc_1_2_proj_a is not None else None,
                "voltage_source_1_0_pu": fonte_tensao, "voltage_source_1_1_pu": fonte_tensao, "voltage_source_1_2_pu": fonte_tensao,
            },
            "aco_m4": { 
                "tensao_1_0_pu_kv": tensao_teste_1_0_kv, "corrente_1_0_pu_a": corrente_excitacao_calc_a,
                "corrente_1_0_pu_perc_nominal_bt": (corrente_excitacao_calc_a / corrente_base_a) * 100 if corrente_base_a > epsilon else 0,
                "potencia_1_0_pu_kva": potencia_ensaio_1pu_aco_kva, "potencia_ativa_1_0_pu_kw": potencia_ativa_aco_1_0_kw,
                "tensao_1_1_pu_kv": tensao_teste_1_1_kv, "corrente_1_1_pu_a": corrente_excitacao_1_1_calc_a,
                "corrente_1_1_pu_perc_nominal_bt": (corrente_excitacao_1_1_calc_a / corrente_base_a) * 100 if corrente_base_a > epsilon else 0,
                "potencia_1_1_pu_kva": potencia_ensaio_1_1pu_aco_kva, "potencia_ativa_1_1_pu_kw": potencia_ativa_aco_1_1_kw,
                "tensao_1_2_pu_kv": tensao_teste_1_2_kv, "corrente_1_2_pu_a": corrente_excitacao_1_2_calc_a,
                "corrente_1_2_pu_perc_nominal_bt": (corrente_excitacao_1_2_calc_a / corrente_base_a) * 100 if corrente_base_a > epsilon else 0,
                "potencia_1_2_pu_kva": potencia_ensaio_1_2pu_aco_kva, "potencia_ativa_1_2_pu_kw": potencia_ativa_aco_1_2_kw,
                "voltage_source_1_0_pu": fonte_tensao, "voltage_source_1_1_pu": fonte_tensao, "voltage_source_1_2_pu": fonte_tensao
            }
        },
        "analise_sut_eps_vazio": sut_eps_analysis_results,
        "tertiary_usage_info": {
            "1.0pu": {"using_terciary": bool(usar_terciario_para_todos), "status": status_geral_terciario, "voltage_source": fonte_tensao},
            "1.1pu": {"using_terciary": bool(usar_terciario_para_todos), "status": status_geral_terciario, "voltage_source": fonte_tensao},
            "1.2pu": {"using_terciary": bool(usar_terciario_para_todos), "status": status_geral_terciario, "voltage_source": fonte_tensao}
        },
        "limites": {
            "dut_power_limit_kw": const.get_dut_power_limit(transformer_data),
            "eps_current_limit_positive_a": eps_current_limit_positive,
            "eps_current_limit_negative_a": eps_current_limit_negative,
            "sut_at_min_voltage_kv": const.SUT_AT_MIN_APPLICABLE_VOLTAGE / 1000,
            "sut_at_max_voltage_kv": const.SUT_AT_MAX_VOLTAGE / 1000,
            "eps_aparente_power_limit_kva": eps_apparent_power_limit,
            "ct_current_limit_a": const.CT_CURRENT_LIMIT
        },
        "validacao_eps_power": validacao_eps_power,
        "validacao_ct_current": validacao_ct_current,
        "validacao_sut_voltage": validacao_sut_voltage
    }


# --- SUT/EPS Compensated Current (Load Losses) ---
def calculate_sut_eps_current_compensated(
    tensao_ref_dut_kv: float,
    corrente_ref_dut_a: float,
    q_efetiva_banco_sf_mvar: Optional[float],
    q_efetiva_banco_cf_mvar: Optional[float],
    tipo_transformador: str,
    V_sut_hv_tap_v: float,
    cf_exists: bool = False,
    grupo_ligacao: str = ""
) -> Dict[str, Any]:
    # Validação de segurança para evitar comparações com None
    if corrente_ref_dut_a is None:
        corrente_ref_dut_a = 0.0
    if tensao_ref_dut_kv is None:
        tensao_ref_dut_kv = 0.0

    # Obter limites EPS dinâmicos baseados no tipo de transformador
    transformer_data = {"tipo_transformador": tipo_transformador, "grupo_ligacao": grupo_ligacao}
    eps_limits = const.get_eps_limits(transformer_data)
    eps_current_limit_positive = eps_limits.get("current_limit_positive_a", const.EPS_CURRENT_LIMIT_POSITIVE)
    eps_current_limit_negative = eps_limits.get("current_limit_negative_a", const.EPS_CURRENT_LIMIT_NEGATIVE)

    # Validação de segurança para evitar comparações com None
    if eps_current_limit_positive is None:
        eps_current_limit_positive = const.EPS_CURRENT_LIMIT_POSITIVE
    if eps_current_limit_negative is None:
        eps_current_limit_negative = const.EPS_CURRENT_LIMIT_NEGATIVE

    q_eff_sf = safe_float(q_efetiva_banco_sf_mvar, 0.0)
    q_eff_cf = safe_float(q_efetiva_banco_cf_mvar, 0.0)

    if not all([tensao_ref_dut_kv, V_sut_hv_tap_v]) or tensao_ref_dut_kv <= epsilon or V_sut_hv_tap_v <= epsilon:
        return {
            "corrente_eps_sf_a": None, "percent_limite_sf": None, 
            "is_over_positive_sf": False, "is_under_negative_sf": False,
            "corrente_eps_cf_a": None, "percent_limite_cf": None, 
            "is_over_positive_cf": False, "is_under_negative_cf": False,
            "i_capacitiva_sf_a": None, "i_capacitiva_cf_a": None,
            "q_efetiva_sf_mvar": q_efetiva_banco_sf_mvar, 
            "q_efetiva_cf_mvar": q_efetiva_banco_cf_mvar if cf_exists else None
        }

    ratio_sut = V_sut_hv_tap_v / const.SUT_BT_VOLTAGE if const.SUT_BT_VOLTAGE > epsilon else 0.0
    sqrt_3 = const.SQRT_3 if normalize_transformer_type(tipo_transformador) == "Trifásico" else 1.0
    
    i_capacitiva_sf = (q_eff_sf * 1000.0) / (tensao_ref_dut_kv * sqrt_3) if (tensao_ref_dut_kv * sqrt_3) > epsilon and q_eff_sf > epsilon else 0.0
    I_eps_net_sf = (corrente_ref_dut_a - i_capacitiva_sf) * ratio_sut
    # Removido fator √3 para monofásico conforme solicitado
    # if normalize_transformer_type(tipo_transformador) == "Monofásico":
    #     I_eps_net_sf = I_eps_net_sf * const.SQRT_3

    is_over_positive_sf_np = I_eps_net_sf > eps_current_limit_positive + epsilon
    is_under_negative_sf_np = I_eps_net_sf < eps_current_limit_negative - epsilon
    percent_limite_sf = (I_eps_net_sf / eps_current_limit_positive) * 100 if eps_current_limit_positive > epsilon else float('inf')

    i_capacitiva_cf, I_eps_net_cf, percent_limite_cf = None, None, None
    is_over_positive_cf_np, is_under_negative_cf_np = False, False

    if cf_exists : 
        if q_eff_cf > epsilon : 
            i_capacitiva_cf = (q_eff_cf * 1000.0) / (tensao_ref_dut_kv * sqrt_3) if (tensao_ref_dut_kv * sqrt_3) > epsilon else 0.0
            I_eps_net_cf = (corrente_ref_dut_a - i_capacitiva_cf) * ratio_sut
        else: 
            i_capacitiva_cf = 0.0
            I_eps_net_cf = corrente_ref_dut_a * ratio_sut 

        # Removido fator √3 para monofásico conforme solicitado
        # if normalize_transformer_type(tipo_transformador) == "Monofásico":
        #     I_eps_net_cf = I_eps_net_cf * const.SQRT_3
        is_over_positive_cf_np = I_eps_net_cf > eps_current_limit_positive + epsilon
        is_under_negative_cf_np = I_eps_net_cf < eps_current_limit_negative - epsilon
        percent_limite_cf = (I_eps_net_cf / eps_current_limit_positive) * 100 if eps_current_limit_positive > epsilon else float('inf')

    return {
        "corrente_eps_sf_a": I_eps_net_sf, 
        "percent_limite_sf": percent_limite_sf, 
        "is_over_positive_sf": bool(is_over_positive_sf_np), 
        "is_under_negative_sf": bool(is_under_negative_sf_np),
        "corrente_eps_cf_a": I_eps_net_cf if cf_exists else None, 
        "percent_limite_cf": percent_limite_cf if cf_exists else None, 
        "is_over_positive_cf": bool(is_over_positive_cf_np) if cf_exists else False, 
        "is_under_negative_cf": bool(is_under_negative_cf_np) if cf_exists else False,
        "i_capacitiva_sf_a": i_capacitiva_sf if q_eff_sf > epsilon else 0.0, 
        "i_capacitiva_cf_a": i_capacitiva_cf if cf_exists and q_eff_cf > epsilon else (0.0 if cf_exists else None),
        "q_efetiva_sf_mvar": q_efetiva_banco_sf_mvar, 
        "q_efetiva_cf_mvar": q_efetiva_banco_cf_mvar if cf_exists else None
    }

# --- Status String Generation Helper (Load Losses) ---
def get_detailed_scenario_status(
    test_voltage_kv: Optional[float],
    q_teste_mvar: Optional[float],
    active_power_kw: Optional[float],
    factor_cap_banc_overvoltage: float = 1.1,
    transformer_data: Optional[Dict[str, str]] = None,
    test_current_a: Optional[float] = None,
    # Banco V≤ (SF)
    sf_bank_nominal_kv: Optional[float] = None,
    sf_eps_ok: bool = True,
    sf_power_ideal_met: bool = True,
    sf_q_efetiva: Optional[float] = None,
    sf_group_info: Optional[str] = None,
    sf_available_configs: Optional[List[Dict[str, Any]]] = None,
    # Banco V> (CF)
    cf_bank_nominal_kv: Optional[float] = None,
    cf_eps_ok: bool = True,
    cf_power_ideal_met: bool = True,
    cf_q_efetiva: Optional[float] = None,
    cf_group_info: Optional[str] = None,
    cf_available_configs: Optional[List[Dict[str, Any]]] = None,
    cf_active: bool = False
    ) -> Dict[str, Any]:

    test_v = safe_float(test_voltage_kv, 0.0)
    q_teste = safe_float(q_teste_mvar, 0.0)
    active_p_kw = safe_float(active_power_kw, 0.0)

    status_v_menor_parts = []
    cap_required_v_menor = None 
    status_v_maior_parts = []
    cap_required_v_maior = None 
    global_status_parts = []


    if sf_bank_nominal_kv and q_teste > epsilon and test_v > epsilon:
        q_banco_necessaria_v_menor = q_teste * (sf_bank_nominal_kv / test_v)**2
        cap_required_v_menor = q_banco_necessaria_v_menor
        voltage_limits_v_menor = const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.get(str(sf_bank_nominal_kv))

        if voltage_limits_v_menor:
            grupo1_max_v_menor = voltage_limits_v_menor["grupo1"]["max"]
            grupo1_2_max_v_menor = voltage_limits_v_menor["grupo1_2"]["max"]

            if q_banco_necessaria_v_menor > grupo1_2_max_v_menor + epsilon:
                status_v_menor_parts.append(f"Q_req V≤ CRÍTICO ({q_banco_necessaria_v_menor:.1f} > {grupo1_2_max_v_menor:.1f} MVAr)")
            elif q_banco_necessaria_v_menor > grupo1_max_v_menor + epsilon:
                status_v_menor_parts.append(f"G1 insuf. V≤, req G1+G2 ({q_banco_necessaria_v_menor:.1f} MVAr)")
            elif sf_group_info and "Grupo 1+2" in sf_group_info:
                # Se o grupo efetivamente selecionado foi G1+G2, mesmo que a potência necessária seja <= G1 max,
                # isso indica que G1 sozinho não foi adequado (por questões de EPS ou outras limitações)
                status_v_menor_parts.append(f"G1 insuf. V≤, req G1+G2 ({q_banco_necessaria_v_menor:.1f} MVAr)")
    
    v_menor_path_eps_ok = sf_eps_ok
    v_maior_path_eps_ok_if_active = cf_eps_ok if cf_active else True 

    if not v_menor_path_eps_ok and not v_maior_path_eps_ok_if_active: 
        status_v_menor_parts.append(f"EPS V≤ > Limite")
    elif not sf_power_ideal_met and q_teste > epsilon : 
        status_v_menor_parts.append("Subcompensada V≤")


    if cf_active:
        if cf_bank_nominal_kv and q_teste > epsilon and test_v > epsilon:
            q_banco_necessaria_v_maior = q_teste * (cf_bank_nominal_kv / test_v)**2
            cap_required_v_maior = q_banco_necessaria_v_maior
            voltage_limits_v_maior = const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.get(str(cf_bank_nominal_kv))

            if voltage_limits_v_maior:
                grupo1_max_v_maior = voltage_limits_v_maior["grupo1"]["max"]
                grupo1_2_max_v_maior = voltage_limits_v_maior["grupo1_2"]["max"]

                if q_banco_necessaria_v_maior > grupo1_2_max_v_maior + epsilon:
                    status_v_maior_parts.append(f"Q_req V> CRÍTICO ({q_banco_necessaria_v_maior:.1f} > {grupo1_2_max_v_maior:.1f} MVAr)")
                elif q_banco_necessaria_v_maior > grupo1_max_v_maior + epsilon:
                    status_v_maior_parts.append(f"G1 insuf. V>, req G1+G2 ({q_banco_necessaria_v_maior:.1f} MVAr)")
                elif cf_group_info and "Grupo 1+2" in cf_group_info:
                    # Se o grupo efetivamente selecionado foi G1+G2, mesmo que a potência necessária seja <= G1 max,
                    # isso indica que G1 sozinho não foi adequado (por questões de EPS ou outras limitações)
                    status_v_maior_parts.append(f"G1 insuf. V>, req G1+G2 ({q_banco_necessaria_v_maior:.1f} MVAr)")
            
            if test_v > epsilon and cf_bank_nominal_kv > epsilon:
                limit_v_maior_cf_bank = cf_bank_nominal_kv * factor_cap_banc_overvoltage
                if test_v > limit_v_maior_cf_bank + epsilon:
                    over_percent = ((test_v / limit_v_maior_cf_bank) - 1) * 100 if limit_v_maior_cf_bank > epsilon else 0
                    status_v_maior_parts.append(f"Vteste V> > Limite ({over_percent:.0f}%)")
        
        v_maior_path_eps_ok = cf_eps_ok
        v_menor_path_eps_ok_for_cf_check = sf_eps_ok

        if not v_maior_path_eps_ok and not v_menor_path_eps_ok_for_cf_check: 
            status_v_maior_parts.append(f"EPS V> > Limite")
        elif not cf_power_ideal_met and q_teste > epsilon: 
            status_v_maior_parts.append("Subcompensada V>")


    max_cap_bank_voltage_overall_kv_list = [k for k_str in const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.keys() if (k := safe_float(k_str)) > 0]
    if max_cap_bank_voltage_overall_kv_list:
        max_cap_bank_voltage_overall = max(max_cap_bank_voltage_overall_kv_list)
        # Apply overvoltage factor to the maximum bank voltage for global validation
        max_cap_bank_voltage_with_factor = max_cap_bank_voltage_overall * factor_cap_banc_overvoltage
        if max_cap_bank_voltage_overall > epsilon and test_v > max_cap_bank_voltage_with_factor + epsilon:
            global_status_parts.append(f"Vteste > Vmax Banco ({max_cap_bank_voltage_overall:.1f}kV)")

    # Obter limite DUT dinâmico
    if transformer_data:
        dut_power_limit = const.get_dut_power_limit(transformer_data)
    else:
        dut_power_limit = const.DUT_POWER_LIMIT  # Fallback para compatibilidade

    if active_p_kw > dut_power_limit + epsilon:
        global_status_parts.append(f"P_DUT > Limite ({dut_power_limit:.0f}kW)")

    # Validação de corrente CT
    if test_current_a is not None and test_current_a > const.CT_CURRENT_LIMIT + epsilon:
        global_status_parts.append(f"🚫 TESTE IMPOSSÍVEL: Itest > CT ({const.CT_CURRENT_LIMIT:.0f}A)")

    # Validação de tensão SUT
    if transformer_data and test_v > epsilon:
        sut_excede_limite, sut_status_msg = check_sut_voltage_limit(test_v, transformer_data)
        if sut_excede_limite:
            # Extrair o limite do SUT da mensagem de status
            if "140" in sut_status_msg:
                global_status_parts.append(f"🚫 TESTE IMPOSSÍVEL: Vtest > SUT (140kV)")
            else:
                global_status_parts.append(f"🚫 TESTE IMPOSSÍVEL: Vtest > SUT")

    if sf_bank_nominal_kv and test_v > (sf_bank_nominal_kv * factor_cap_banc_overvoltage) + epsilon:
        if not any(f"Vteste > Vmax Banco ({max_cap_bank_voltage_overall:.1f}kV)" in s for s in global_status_parts): # Avoid duplicate if global max already caught it
            if not any("Vteste > Vmax Banco" in s for s in global_status_parts): # Check for generic factor message
                 global_status_parts.append(f"Vteste > Vmax Banco") 


    cap_required_max = max(cap_required_v_menor or 0, cap_required_v_maior or 0)
    
    status_v_menor_final = " | ".join(status_v_menor_parts) if status_v_menor_parts else "OK"
    status_v_maior_final = " | ".join(status_v_maior_parts) if status_v_maior_parts and cf_active else ("N/A" if not cf_active else "OK")
    status_global_final = " | ".join(global_status_parts) if global_status_parts else "OK"
    
    return {
        "status_v_menor": status_v_menor_final,
        "status_v_maior": status_v_maior_final,
        "status_global": status_global_final, 
        "cap_required_mvar": cap_required_max if cap_required_max > epsilon else None
    }

# Manter função original para compatibilidade
def get_scenario_status_string(
    test_voltage_kv: Optional[float],
    selected_bank_nominal_kv: Optional[float],
    q_teste_mvar: Optional[float],
    active_power_kw: Optional[float],
    factor_cap_banc_overvoltage: float = 1.1,
    is_cf_bank_active_and_has_power: bool = False, 
    is_eps_ok_for_selected_path: bool = True,
    power_ideal_met_for_selected_path: bool = True,
    sf_bank_nominal_kv_param: Optional[float] = None,
    sf_eps_ok_param: bool = True,
    sf_power_ideal_met_param: bool = True,
    cf_bank_nominal_kv_param: Optional[float] = None,
    cf_eps_ok_param: bool = True,
    cf_power_ideal_met_param: bool = True,
    cf_active_param: bool = False
    ) -> Tuple[str, Optional[float]]:

    detailed_status = get_detailed_scenario_status(
        test_voltage_kv, q_teste_mvar, active_power_kw, factor_cap_banc_overvoltage,
        transformer_data=None,  # Não temos acesso aos dados do transformador nesta função
        test_current_a=None,  # Não temos acesso à corrente de teste nesta função
        sf_bank_nominal_kv=sf_bank_nominal_kv_param,
        sf_eps_ok=sf_eps_ok_param,
        sf_power_ideal_met=sf_power_ideal_met_param,
        cf_bank_nominal_kv=cf_bank_nominal_kv_param,
        cf_eps_ok=cf_eps_ok_param,
        cf_power_ideal_met=cf_power_ideal_met_param,
        cf_active=cf_active_param
    )
    
    final_status_parts = []
    if detailed_status["status_global"] != "OK":
        final_status_parts.append(detailed_status["status_global"])
    
    if detailed_status["status_v_menor"] != "OK":
        is_v_menor_covered_by_global = False
        if "Vteste > Vmax Banco" in detailed_status["status_global"] and \
           ("Q_req V≤ CRÍTICO" not in detailed_status["status_v_menor"] and "G1 insuf. V≤" not in detailed_status["status_v_menor"]):
            is_v_menor_covered_by_global = True 
            
        if not is_v_menor_covered_by_global:
             final_status_parts.append(detailed_status["status_v_menor"])

    if detailed_status["status_v_maior"] not in ["OK", "N/A"]:
        final_status_parts.append(detailed_status["status_v_maior"])
        
    combined_status = " | ".join(final_part for final_part in final_status_parts if final_part) if final_status_parts else "OK"
    return combined_status, detailed_status["cap_required_mvar"]

# --- Load Losses Calculation (Main Function) ---
def calculate_load_losses(data_in: Dict[str, Any]) -> Dict[str, Any]:
    try: data = LoadLossesInput(**data_in)
    except Exception as e: log.error(f"Erro de validação para LoadLossesInput: {e}"); raise ValueError(f"Dados de perdas em carga inválidos: {e}")
    
    sqrt_3_factor = const.SQRT_3 if normalize_transformer_type(data.tipo_transformador) == "Trifásico" else 1.0
    # Validação de segurança para evitar comparações com None
    perdas_carga_kw_u_nom_safe = data.perdas_carga_kw_u_nom if data.perdas_carga_kw_u_nom is not None else 0.0
    perdas_vazio_kw_calculada_safe = data.perdas_vazio_kw_calculada if data.perdas_vazio_kw_calculada is not None else 0.0
    perdas_carga_sem_vazio_nom = perdas_carga_kw_u_nom_safe - perdas_vazio_kw_calculada_safe
    if perdas_carga_sem_vazio_nom <= epsilon and perdas_carga_kw_u_nom_safe > perdas_vazio_kw_calculada_safe :
        log.warning(f"Perdas de carga nominais (sem perdas em vazio) são muito baixas ou negativas ({perdas_carga_sem_vazio_nom:.3f} kW).")
    
    # Validação de segurança para evitar comparações com None
    temperatura_referencia_safe = data.temperatura_referencia if data.temperatura_referencia is not None else 75.0
    temp_factor_ref_to_25c = (235.0 + 25.0) / (235.0 + temperatura_referencia_safe)
    # Validação de segurança para evitar comparações com None
    potencia_mva_safe = data.potencia_mva if data.potencia_mva is not None else 0.0
    tensao_at_kv_safe = data.tensao_at_kv if data.tensao_at_kv is not None else 0.0
    corrente_nominal_at_a_safe = data.corrente_nominal_at_a if data.corrente_nominal_at_a is not None else 0.0
    r_percent_nom = (perdas_carga_sem_vazio_nom * 1000 / (potencia_mva_safe * 1e6)) * 100 if potencia_mva_safe > epsilon else 0
    # Validação de segurança para evitar comparações com None
    impedancia_safe = data.impedancia if data.impedancia is not None else 0.0
    x_percent_nom = math.sqrt(max(0, impedancia_safe**2 - r_percent_nom**2)) if impedancia_safe >= r_percent_nom else 0
    fp_cc_nom = r_percent_nom / impedancia_safe if impedancia_safe > epsilon else 0
    
    condicoes_nominais_results = {
        "temperatura_referencia": temperatura_referencia_safe, "tensao_at_kv_nominal": tensao_at_kv_safe,
        "corrente_at_a_nominal": corrente_nominal_at_a_safe, "vcc_percent_nominal": impedancia_safe,
        "vcc_kv_nominal": (tensao_at_kv_safe / 100.0) * impedancia_safe,
        "perdas_totais_kw_nominal_tref": perdas_carga_kw_u_nom_safe,
        "perdas_carga_s_vazio_kw_nominal_tref": perdas_carga_sem_vazio_nom,
        "perdas_frio_25c_kw_nominal": perdas_carga_sem_vazio_nom * temp_factor_ref_to_25c,
        "perdas_vazio_kw_usada_calculo": perdas_vazio_kw_calculada_safe,
        "r_percent_calc_nominal": r_percent_nom, "x_percent_calc_nominal": x_percent_nom, 
        "fp_cc_calc_nominal": fp_cc_nom,
        "tensao_at_kv_menor": data.tensao_at_tap_menor_kv, "corrente_at_a_menor": data.corrente_nominal_at_tap_menor_a,
        "vcc_percent_menor": data.impedancia_tap_menor, "vcc_kv_menor": (data.tensao_at_tap_menor_kv / 100.0) * data.impedancia_tap_menor if data.tensao_at_tap_menor_kv is not None and data.impedancia_tap_menor is not None else 0.0,
        "perdas_totais_kw_menor_tref": data.perdas_carga_kw_u_min, "perdas_carga_s_vazio_kw_menor_tref": (data.perdas_carga_kw_u_min - perdas_vazio_kw_calculada_safe) if data.perdas_carga_kw_u_min is not None else 0.0,
        "perdas_frio_25c_kw_menor": ((data.perdas_carga_kw_u_min - perdas_vazio_kw_calculada_safe) * temp_factor_ref_to_25c) if data.perdas_carga_kw_u_min is not None else 0.0,
        "tensao_at_kv_maior": data.tensao_at_tap_maior_kv, "corrente_at_a_maior": data.corrente_nominal_at_tap_maior_a,
        "vcc_percent_maior": data.impedancia_tap_maior, "vcc_kv_maior": (data.tensao_at_tap_maior_kv / 100.0) * data.impedancia_tap_maior if data.tensao_at_tap_maior_kv is not None and data.impedancia_tap_maior is not None else 0.0,
        "perdas_totais_kw_maior_tref": data.perdas_carga_kw_u_max, "perdas_carga_s_vazio_kw_maior_tref": (data.perdas_carga_kw_u_max - perdas_vazio_kw_calculada_safe) if data.perdas_carga_kw_u_max is not None else 0.0,
        "perdas_frio_25c_kw_maior": ((data.perdas_carga_kw_u_max - perdas_vazio_kw_calculada_safe) * temp_factor_ref_to_25c) if data.perdas_carga_kw_u_max is not None else 0.0
    }
    
    cenarios_config_list = [
        ("Nominal", tensao_at_kv_safe, corrente_nominal_at_a_safe, impedancia_safe, perdas_carga_kw_u_nom_safe),
        ("Menor", data.tensao_at_tap_menor_kv, data.corrente_nominal_at_tap_menor_a, data.impedancia_tap_menor, data.perdas_carga_kw_u_min),
        ("Maior", data.tensao_at_tap_maior_kv, data.corrente_nominal_at_tap_maior_a, data.impedancia_tap_maior, data.perdas_carga_kw_u_max)
    ]
    
    all_taps_data, max_overall_test_v_kv, max_overall_q_banco_necessaria_nominal, geral_status_alerts, problematic_scenarios_for_q_limits = [], 0.0, 0.0, set(), []
    test_types_standard = ["25°C", "Frio Inicio de Elevacao de Temperatura", "Quente"]
    test_types_overload = ["1.2 pu", "1.4 pu"]

    # Check if overload scenarios should be included based on voltage conditions
    include_overload_scenarios = should_include_overload_scenarios(data)

    for tap_label, Vnom_kv_tap_dut, Inom_a_tap_dut, Z_percent_tap_dut, Pcarga_total_kw_tap_tref in cenarios_config_list:
        current_tap_output_data = {"nome_tap": tap_label, "cenarios_do_tap": []}
        # Validação de segurança para evitar comparações com None
        Pcarga_total_kw_tap_tref_safe = Pcarga_total_kw_tap_tref if Pcarga_total_kw_tap_tref is not None else 0.0
        Inom_a_tap_dut_safe = Inom_a_tap_dut if Inom_a_tap_dut is not None else 0.0
        Pcarga_sem_vazio_kw_tap_tref = Pcarga_total_kw_tap_tref_safe - perdas_vazio_kw_calculada_safe

        Pcc_frio_kw_tap_25c = Pcarga_sem_vazio_kw_tap_tref * temp_factor_ref_to_25c
        # Validação de segurança para evitar comparações com None
        Vnom_kv_tap_dut_safe = Vnom_kv_tap_dut if Vnom_kv_tap_dut is not None else 0.0
        Z_percent_tap_dut_safe = Z_percent_tap_dut if Z_percent_tap_dut is not None else 0.0
        Vcc_kv_tap_dut = (Vnom_kv_tap_dut_safe / 100.0) * Z_percent_tap_dut_safe

        current_test_types = list(test_types_standard)
        if include_overload_scenarios:
            current_test_types.extend(test_types_overload)
            log.info(f"Including overload scenarios for tap {tap_label}: {test_types_overload}")
        else:
            log.info(f"Excluding overload scenarios for tap {tap_label} - voltage conditions not met")

        for test_type_label in current_test_types:
            scen_res_dict: Dict[str, Any] = {"nome_cenario_teste": test_type_label}
            Vtest_kv_scen, Itest_a_scen, Pativa_kw_scen = 0.0, 0.0, 0.0
            
            if test_type_label == "25°C": 
                Vtest_kv_scen, Itest_a_scen, Pativa_kw_scen = Vcc_kv_tap_dut, Inom_a_tap_dut_safe, Pcc_frio_kw_tap_25c
            elif test_type_label == "Frio Inicio de Elevacao de Temperatura":
                if Pcc_frio_kw_tap_25c > epsilon:
                    ratio_frio_inicio = Pcarga_sem_vazio_kw_tap_tref / Pcc_frio_kw_tap_25c
                else:
                    ratio_frio_inicio = 1.0
                    if Pcarga_sem_vazio_kw_tap_tref > epsilon:
                        log.warning(f"Pcc_frio_kw_tap_25c é zero ou próximo de zero ({Pcc_frio_kw_tap_25c:.4f} kW) para {tap_label} - '{test_type_label}', mas Pcarga_sem_vazio_kw_tap_tref ({Pcarga_sem_vazio_kw_tap_tref:.4f} kW) não é. Usando ratio_frio_inicio = 1.0. Cálculos podem ser imprecisos.")

                Vtest_kv_scen = Vcc_kv_tap_dut * ratio_frio_inicio
                Itest_a_scen = Inom_a_tap_dut_safe * ratio_frio_inicio
                Pativa_kw_scen = Pcarga_sem_vazio_kw_tap_tref

            elif test_type_label == "Quente":
                if Pcc_frio_kw_tap_25c > epsilon and Pcarga_sem_vazio_kw_tap_tref > epsilon:
                    Vtest_kv_scen = Vcc_kv_tap_dut * math.sqrt(Pcarga_sem_vazio_kw_tap_tref / Pcc_frio_kw_tap_25c)
                else:
                    Vtest_kv_scen = Vcc_kv_tap_dut
                    if Pcarga_sem_vazio_kw_tap_tref > epsilon:
                        log.warning(f"Pcc_frio_kw_tap_25c é zero para {tap_label}, {test_type_label}. Vtest_kv_scen para Quente pode ser impreciso.")
                Itest_a_scen = Inom_a_tap_dut_safe
                Pativa_kw_scen = Pcarga_sem_vazio_kw_tap_tref
            elif "pu" in test_type_label: 
                pu_factor = 1.2 if "1.2" in test_type_label else 1.4
                Vtest_kv_scen, Itest_a_scen = Vcc_kv_tap_dut * pu_factor, Inom_a_tap_dut * pu_factor
                Pativa_kw_scen = Pcarga_sem_vazio_kw_tap_tref * (pu_factor**2)
            
            S_teste_kva = Vtest_kv_scen * Itest_a_scen * sqrt_3_factor
            Q_teste_mvar_scen = math.sqrt(max(0, S_teste_kva**2 - Pativa_kw_scen**2)) / 1000.0 if S_teste_kva >= Pativa_kw_scen - epsilon else 0.0
            
            scen_res_dict["test_params_cenario"] = {"tensao_kv": Vtest_kv_scen, "corrente_a": Itest_a_scen, "pativa_kw": Pativa_kw_scen, "pteste_mva": S_teste_kva / 1000.0, "q_teste_mvar": Q_teste_mvar_scen}
            max_overall_test_v_kv = max(max_overall_test_v_kv, Vtest_kv_scen or 0)
            
            target_v_sf_key_scen, target_v_cf_key_scen = select_target_bank_voltage_keys(Vtest_kv_scen, data.factor_cap_banc_overvoltage, data.tipo_transformador)

            if target_v_sf_key_scen and Vtest_kv_scen > epsilon and float(target_v_sf_key_scen) > epsilon and Q_teste_mvar_scen > epsilon:
                 q_banco_necessaria_nominal_cenario = Q_teste_mvar_scen * (float(target_v_sf_key_scen) / Vtest_kv_scen)**2
                 max_overall_q_banco_necessaria_nominal = max(max_overall_q_banco_necessaria_nominal, q_banco_necessaria_nominal_cenario)
            
            cf_bank_available_for_scenario = bool(target_v_cf_key_scen is not None) 

            v_sut_hv_tap_v_referencia_para_otim_q_corrigida = Vtest_kv_scen * 1000.0

            group_sf, solutions_list_sf = _select_group_and_get_ranked_options(
                target_v_sf_key_scen,
                Q_teste_mvar_scen, # Passando Q_teste_mvar_scen
                Vtest_kv_scen,
                Itest_a_scen,
                data.tipo_transformador,
                v_sut_hv_tap_v_referencia_para_otim_q_corrigida,
                data.grupo_ligacao
            )
            scen_res_dict["cap_bank_sf"] = _build_cap_bank_output_structure(target_v_sf_key_scen, group_sf, solutions_list_sf, data.tipo_transformador)

            group_cf, solutions_list_cf = _select_group_and_get_ranked_options(
                target_v_cf_key_scen,
                Q_teste_mvar_scen, # Passando Q_teste_mvar_scen
                Vtest_kv_scen,
                Itest_a_scen,
                data.tipo_transformador,
                v_sut_hv_tap_v_referencia_para_otim_q_corrigida,
                data.grupo_ligacao
            )
            scen_res_dict["cap_bank_cf"] = _build_cap_bank_output_structure(target_v_cf_key_scen, group_cf, solutions_list_cf, data.tipo_transformador)

            # Definir as informações do grupo para uso no status
            grupo_info_sf_initial = group_sf
            grupo_info_cf_initial = group_cf

            initial_sf_config_list = scen_res_dict["cap_bank_sf"].get("available_configurations", [])
            initial_sf_config = next((c for c in initial_sf_config_list if c.get("is_initial_display_default")), 
                                     initial_sf_config_list[0] if initial_sf_config_list else {})

            initial_cf_config_list = scen_res_dict["cap_bank_cf"].get("available_configurations", [])
            initial_cf_config = next((c for c in initial_cf_config_list if c.get("is_initial_display_default")),
                                     initial_cf_config_list[0] if initial_cf_config_list else {})

            q_eff_sf_initial = initial_sf_config.get("q_efetiva_banco_mvar", 0.0)
            q_eff_cf_initial = initial_cf_config.get("q_efetiva_banco_mvar", 0.0) if cf_bank_available_for_scenario else 0.0
            
            power_ideal_sf_initial = initial_sf_config.get("meets_ideal_power_req", False)
            power_ideal_cf_initial = initial_cf_config.get("meets_ideal_power_req", False) if cf_bank_available_for_scenario else True 
            
            is_cf_bank_active_and_has_power_for_status_initial = bool(target_v_cf_key_scen and q_eff_cf_initial > epsilon)
            
            scen_res_dict["i_capacitiva_sf_a"] = (q_eff_sf_initial * 1000) / (Vtest_kv_scen * sqrt_3_factor) if Vtest_kv_scen * sqrt_3_factor > epsilon and q_eff_sf_initial > epsilon else None
            scen_res_dict["i_capacitiva_cf_a"] = (q_eff_cf_initial * 1000) / (Vtest_kv_scen * sqrt_3_factor) if is_cf_bank_active_and_has_power_for_status_initial and (Vtest_kv_scen * sqrt_3_factor > epsilon) else None
            scen_res_dict["q_efetiva_banco_sf_mvar"] = q_eff_sf_initial
            scen_res_dict["q_efetiva_banco_cf_mvar"] = q_eff_cf_initial if cf_bank_available_for_scenario else None


            sut_eps_taps_info_list = []
            final_eps_ok_for_sf_at_ideal_sut_initial = False
            final_eps_ok_for_cf_at_ideal_sut_initial = False

            # Determinar qual saída do SUT usar baseado na tensão de teste e configuração do DUT
            transformer_data = get_transformer_data_for_limits(data)
            sut_source_type, available_sut_voltages_v, sut_display_msg = select_optimal_sut_source_for_dut(Vtest_kv_scen, transformer_data)
            all_sut_taps_v = available_sut_voltages_v

            log.info(f"[Load Losses] {tap_label} - {test_type_label}: {sut_display_msg}")

            if not all_sut_taps_v:
                log.warning(f"Nenhum tap SUT {sut_source_type} disponível para cenário {tap_label} - {test_type_label}.")
            else:
                target_sut_display_ref_v = Vtest_kv_scen * 1000.0

                # Selecionar tensão ótima do SUT
                ideal_sut_tap_v_for_scenario = select_optimal_sut_voltage(Vtest_kv_scen, all_sut_taps_v)

                if ideal_sut_tap_v_for_scenario:
                    comp_curr_at_ideal_sut = calculate_sut_eps_current_compensated(
                        Vtest_kv_scen, Itest_a_scen,
                        q_eff_sf_initial, q_eff_cf_initial,
                        data.tipo_transformador, ideal_sut_tap_v_for_scenario,
                        cf_exists=is_cf_bank_active_and_has_power_for_status_initial,
                        grupo_ligacao=data.grupo_ligacao
                    )
                    if comp_curr_at_ideal_sut["corrente_eps_sf_a"] is not None and \
                       not (comp_curr_at_ideal_sut["is_over_positive_sf"] or comp_curr_at_ideal_sut["is_under_negative_sf"]):
                        final_eps_ok_for_sf_at_ideal_sut_initial = True
                    
                    if is_cf_bank_active_and_has_power_for_status_initial:
                        if comp_curr_at_ideal_sut["corrente_eps_cf_a"] is not None and \
                           not (comp_curr_at_ideal_sut["is_over_positive_cf"] or comp_curr_at_ideal_sut["is_under_negative_cf"]):
                            final_eps_ok_for_cf_at_ideal_sut_initial = True
                    else: 
                        final_eps_ok_for_cf_at_ideal_sut_initial = True


                # Configurar taps para display baseado no tipo de saída do SUT
                display_sut_taps_v = []
                if sut_source_type == "SUT_TERC":
                    # Para terciário, mostrar apenas as tensões disponíveis (3.5kV, 7kV, 14kV)
                    display_sut_taps_v = all_sut_taps_v
                    log.info(f"[Load Losses] {tap_label} - {test_type_label}: Usando terciário do SUT - tensões disponíveis: {[v/1000 for v in all_sut_taps_v]}kV")
                else:
                    # Para AT, usar lógica existente de seleção de taps próximos
                    if ideal_sut_tap_v_for_scenario:
                        ideal_idx = -1
                        try: ideal_idx = all_sut_taps_v.index(ideal_sut_tap_v_for_scenario)
                        except ValueError: pass
                        start_idx = max(0, ideal_idx - 2)
                        end_idx = min(len(all_sut_taps_v), ideal_idx + 3)
                        display_sut_taps_v = all_sut_taps_v[start_idx:end_idx]
                    else:
                        display_sut_taps_v = all_sut_taps_v[:5]

                    log.info(f"[Load Losses] {tap_label} - {test_type_label}: Usando AT do SUT - tap ideal: {ideal_sut_tap_v_for_scenario/1000:.1f}kV")


                for V_sut_tap_v_real in display_sut_taps_v:
                    comp_curr_detailed = calculate_sut_eps_current_compensated(
                        Vtest_kv_scen, Itest_a_scen,
                        q_eff_sf_initial, q_eff_cf_initial,
                        data.tipo_transformador, V_sut_tap_v_real,
                        cf_exists=is_cf_bank_active_and_has_power_for_status_initial,
                        grupo_ligacao=data.grupo_ligacao
                    )
                    is_ideal_tap_flag = (ideal_sut_tap_v_for_scenario is not None and abs(V_sut_tap_v_real - ideal_sut_tap_v_for_scenario) < epsilon)

                    has_sf_data = comp_curr_detailed.get("corrente_eps_sf_a") is not None
                    has_cf_data = comp_curr_detailed.get("corrente_eps_cf_a") is not None and is_cf_bank_active_and_has_power_for_status_initial

                    if has_sf_data or has_cf_data:
                        sut_eps_taps_info_list.append({
                            "sut_tap_kv": V_sut_tap_v_real / 1000.0,
                            "is_ideal_tap": is_ideal_tap_flag,
                            "vtest_kv": Vtest_kv_scen,
                            "sut_source_type": sut_source_type,  # Indica se é SUT_TERC ou SUT_AT
                            "sut_display_msg": sut_display_msg,  # Mensagem explicativa
                            **comp_curr_detailed
                        })
            sut_eps_taps_info_list.sort(key=lambda x: x["sut_tap_kv"])
            scen_res_dict["sut_eps_analysis"] = sut_eps_taps_info_list

            detailed_status_results = get_detailed_scenario_status(
                test_voltage_kv=Vtest_kv_scen,
                q_teste_mvar=Q_teste_mvar_scen,
                active_power_kw=Pativa_kw_scen,
                transformer_data=get_transformer_data_for_limits(data),
                factor_cap_banc_overvoltage=data.factor_cap_banc_overvoltage,
                test_current_a=Itest_a_scen,
                sf_bank_nominal_kv=float(target_v_sf_key_scen) if target_v_sf_key_scen else None,
                sf_eps_ok=final_eps_ok_for_sf_at_ideal_sut_initial,
                sf_power_ideal_met=power_ideal_sf_initial,
                sf_group_info=grupo_info_sf_initial,
                sf_available_configs=initial_sf_config_list,
                cf_bank_nominal_kv=float(target_v_cf_key_scen) if target_v_cf_key_scen else None,
                cf_eps_ok=final_eps_ok_for_cf_at_ideal_sut_initial,
                cf_power_ideal_met=power_ideal_cf_initial,
                cf_group_info=grupo_info_cf_initial,
                cf_available_configs=initial_cf_config_list,
                cf_active=is_cf_bank_active_and_has_power_for_status_initial
            )
            
            scen_res_dict["status_v_menor"] = detailed_status_results["status_v_menor"]
            scen_res_dict["status_v_maior"] = detailed_status_results["status_v_maior"]
            scen_res_dict["status_global"] = detailed_status_results["status_global"]
            scen_res_dict["cap_required_mvar"] = detailed_status_results["cap_required_mvar"]

            combined_status_parts = []
            if detailed_status_results["status_global"] != "OK": combined_status_parts.append(detailed_status_results["status_global"])
            if detailed_status_results["status_v_menor"] != "OK": combined_status_parts.append(detailed_status_results["status_v_menor"])
            if detailed_status_results["status_v_maior"] not in ["OK", "N/A"]: combined_status_parts.append(detailed_status_results["status_v_maior"])
            status_str_combined = " | ".join(final_part for final_part in combined_status_parts if final_part) if combined_status_parts else "OK"
            scen_res_dict["status_cenario"] = status_str_combined


            if "CRÍTICO" in status_str_combined or "P_DUT > Limite" in status_str_combined or "Vteste > Vmax Banco" in status_str_combined or "TESTE IMPOSSÍVEL" in status_str_combined:
                 if "EPS V≤ > Limite" in detailed_status_results["status_v_menor"] or "EPS V> > Limite" in detailed_status_results["status_v_maior"]: geral_status_alerts.add("EPS_Limite_Excedido")
                 if "Vteste > Vmax Banco" in detailed_status_results["status_global"] and "kV)" in detailed_status_results["status_global"]: geral_status_alerts.add("V_Banco_Max_Global_Absoluto_Excedido")
                 elif "Vteste > Vmax Banco" in detailed_status_results["status_global"]: geral_status_alerts.add("V_Banco_Max_Fator_Excedido")
                 if "P_DUT > Limite" in detailed_status_results["status_global"]: geral_status_alerts.add("P_DUT_Limite_Critico")
                 if "Itest > CT" in detailed_status_results["status_global"]: geral_status_alerts.add("CT_Current_Exceeded")
                 if "Vtest > SUT" in detailed_status_results["status_global"]: geral_status_alerts.add("SUT_Voltage_Exceeded")
                 if "Q_req V≤ CRÍTICO" in detailed_status_results["status_v_menor"] or "Q_req V> CRÍTICO" in detailed_status_results["status_v_maior"]:
                    geral_status_alerts.add("Q_Banco_Critico")
                    problematic_scenarios_for_q_limits.append(f"{tap_label}-{test_type_label}(CRÍTICO Q_Req)")
            elif "ALERTA" in status_str_combined or "G1 insuf" in status_str_combined or "Subcompensada" in status_str_combined:
                if "G1 insuf" in detailed_status_results["status_v_menor"] or "G1 insuf" in detailed_status_results["status_v_maior"]:
                    geral_status_alerts.add("G1_Nao_Suficiente")
                    problematic_scenarios_for_q_limits.append(f"{tap_label}-{test_type_label}(G1 Insuficiente)")
                if "Subcompensada" in detailed_status_results["status_v_menor"] or "Subcompensada" in detailed_status_results["status_v_maior"]:
                    geral_status_alerts.add("Subcompensado_EPS_OK")
                if "Vteste V> > Limite" in detailed_status_results["status_v_maior"]:
                    geral_status_alerts.add("V_CF_Sobretensao_Limite")

            # Verificação adicional para G1 insuficiente (independente do status_str_combined)
            if "G1 insuf" in detailed_status_results["status_v_menor"] or "G1 insuf" in detailed_status_results["status_v_maior"]:
                geral_status_alerts.add("G1_Nao_Suficiente")
                if f"{tap_label}-{test_type_label}(G1 Insuficiente)" not in problematic_scenarios_for_q_limits:
                    problematic_scenarios_for_q_limits.append(f"{tap_label}-{test_type_label}(G1 Insuficiente)")


            current_tap_output_data["cenarios_do_tap"].append(scen_res_dict)
        all_taps_data.append(current_tap_output_data)

    # Obter limites EPS dinâmicos para uso geral
    transformer_data = get_transformer_data_for_limits(data)
    eps_limits = const.get_eps_limits(transformer_data)

    geral_banco_info_str = "Não aplicável ou não requerido."
    target_v_geral_sf_key, _ = select_target_bank_voltage_keys(max_overall_test_v_kv, data.factor_cap_banc_overvoltage, data.tipo_transformador)
    q_banco_necessaria_geral_para_v_geral_sf = 0.0
    if target_v_geral_sf_key and max_overall_test_v_kv > epsilon and float(target_v_geral_sf_key) > epsilon and max_overall_q_banco_necessaria_nominal > epsilon:
        q_banco_necessaria_geral_para_v_geral_sf = max_overall_q_banco_necessaria_nominal
    
    if "CT_Current_Exceeded" in geral_status_alerts:
        geral_banco_info_str = f"🚨 CRÍTICO: Corrente de teste excede limite CT ({const.CT_CURRENT_LIMIT:.0f}A) em um ou mais cenários."
    elif "P_DUT_Limite_Critico" in geral_status_alerts:
        dut_power_limit = const.get_dut_power_limit(transformer_data)
        geral_banco_info_str = f"🚨 CRÍTICO: Potência DUT excede limite ({dut_power_limit}kW) em um ou mais cenários."
    elif "V_Banco_Max_Global_Absoluto_Excedido" in geral_status_alerts:
        max_overall_bank_v = max([float(k_str) for k_str in const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.keys() if (k_str and safe_float(k_str) > 0)], default=0)
        geral_banco_info_str = f"🚨 CRÍTICO: Tensão de teste excede Vmax global do banco ({max_overall_bank_v:.1f}kV) em um ou mais cenários."
    elif "V_Banco_Max_Fator_Excedido" in geral_status_alerts:
         geral_banco_info_str = f"🚨 CRÍTICO: Tensão de teste excede Vbanco * fator sobretensão em um ou mais cenários."
    elif "EPS_Limite_Excedido" in geral_status_alerts:
        # Usar limites EPS dinâmicos para a mensagem
        eps_current_limit_positive = eps_limits.get("current_limit_positive_a", const.EPS_CURRENT_LIMIT_POSITIVE)
        eps_current_limit_negative = eps_limits.get("current_limit_negative_a", const.EPS_CURRENT_LIMIT_NEGATIVE)

        # Validação de segurança para evitar comparações com None
        if eps_current_limit_positive is None:
            eps_current_limit_positive = const.EPS_CURRENT_LIMIT_POSITIVE
        if eps_current_limit_negative is None:
            eps_current_limit_negative = const.EPS_CURRENT_LIMIT_NEGATIVE
        geral_banco_info_str = f"🚨 CRÍTICO: Corrente EPS excede limites ({eps_current_limit_negative}A a {eps_current_limit_positive}A) em um ou mais cenários."
    elif "Q_Banco_Critico" in geral_status_alerts and target_v_geral_sf_key:
        limits_geral = const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.get(target_v_geral_sf_key)
        g1_2_max = limits_geral["grupo1_2"]["max"] if limits_geral else "N/D"
        geral_banco_info_str = f"🚨 CRÍTICO: Q máx. nominal necessária ({q_banco_necessaria_geral_para_v_geral_sf:.1f} MVAr) > capacidade G1+2 ({g1_2_max} MVAr) do banco {target_v_geral_sf_key}kV. Cenários: {', '.join(problematic_scenarios_for_q_limits[:2])}."
    elif "G1_Nao_Suficiente" in geral_status_alerts and target_v_geral_sf_key:
        limits_geral = const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.get(target_v_geral_sf_key)
        g1_max = limits_geral["grupo1"]["max"] if limits_geral else "N/D"
        geral_banco_info_str = f"⚠️ ALERTA: Q máx. nominal necessária ({q_banco_necessaria_geral_para_v_geral_sf:.1f} MVAr) > capacidade G1 ({g1_max} MVAr) do banco {target_v_geral_sf_key}kV. Requer G1+2."
    elif "Subcompensado_EPS_OK" in geral_status_alerts:
        geral_banco_info_str = f"⚠️ ALERTA: Alguns cenários usam potência subcompensada para manter I_EPS OK. Q máx. nominal necessária ({q_banco_necessaria_geral_para_v_geral_sf:.1f} MVAr)."
    elif "V_CF_Sobretensao_Limite" in geral_status_alerts:
        geral_banco_info_str = f"⚠️ ALERTA: Tensão de teste excede Vbanco_CF * fator sobretensão em um ou mais cenários V>."
    elif q_banco_necessaria_geral_para_v_geral_sf > epsilon and target_v_geral_sf_key:
        limits_geral = const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE.get(target_v_geral_sf_key)
        if limits_geral:
            if q_banco_necessaria_geral_para_v_geral_sf <= limits_geral["grupo1"]["max"] + epsilon:
                 geral_banco_info_str = f"✅ POSSÍVEL COM GRUPO 1: Q máx. nominal necessária ({q_banco_necessaria_geral_para_v_geral_sf:.1f} MVAr) <= capacidade G1 do banco {target_v_geral_sf_key}kV ({limits_geral['grupo1']['max']:.1f} MVAr)."
        else:
            geral_banco_info_str = f"⚠️ ALERTA: Não foi possível determinar limites de potência para o banco geral {target_v_geral_sf_key}kV."
    elif not geral_status_alerts:
        geral_banco_info_str = "✅ OK: Testes não requerem compensação reativa significativa ou todos os requisitos são atendidos."


    condicoes_nominais_results["info_geral_banco_capacitores"] = geral_banco_info_str
    condicoes_nominais_results["alertas_gerais_status"] = sorted(list(geral_status_alerts))
    
    trifasico_v_keys_str = [str(v) for v in const.EPS_CAP_BANK_VOLTAGES_KV]
    monofasico_v_keys_str = const.MONOPHASE_VOLTAGES 

    relevant_voltages_keys = monofasico_v_keys_str if normalize_transformer_type(data.tipo_transformador) == "Monofásico" else trifasico_v_keys_str
    
    relevant_capacitor_power_limits = { 
        v_str: const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE[v_str] 
        for v_str in relevant_voltages_keys 
        if v_str in const.CAPACITOR_POWER_LIMITS_BY_VOLTAGE 
    }

    return {
        "condicoes_nominais": condicoes_nominais_results,
        "cenarios_detalhados_por_tap": all_taps_data,
        "limites_info": {
            "eps_current_limit_positive_a": eps_limits.get("current_limit_positive_a", const.EPS_CURRENT_LIMIT_POSITIVE),
            "eps_current_limit_negative_a": eps_limits.get("current_limit_negative_a", const.EPS_CURRENT_LIMIT_NEGATIVE),
            "dut_power_limit_kw": const.get_dut_power_limit(transformer_data),
            "capacitor_power_limits_by_voltage": relevant_capacitor_power_limits
        }
    }