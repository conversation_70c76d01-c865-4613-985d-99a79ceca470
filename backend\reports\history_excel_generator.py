"""
Gerador de relatórios Excel para dados históricos do sistema TTS.
Cria arquivos Excel com todos os dados históricos separados por tipo de transformador.
"""

import io
import json
import pandas as pd
from typing import Dict, Any, List
from datetime import datetime


def generate_history_excel_report(sessions: List[Dict[str, Any]]) -> io.BytesIO:
    """
    Gera um arquivo Excel com todos os dados históricos.
    Separa dados em planilhas para transformadores trifásicos e monofásicos.
    
    Args:
        sessions: Lista de sessões históricas
        
    Returns:
        BytesIO: Buffer com o arquivo Excel gerado
    """
    buffer = io.BytesIO()
    
    # Criar um ExcelWriter
    with pd.ExcelWriter(buffer, engine='openpyxl') as writer:
        
        # Adicionar uma planilha de resumo
        create_summary_sheet(writer, sessions)
        
        # Separar dados por tipo de transformador
        trifasico_data = []
        monofasico_data = []
        
        for session in sessions:
            session_data = extract_session_data(session)
            if session_data:
                transformer_type = get_transformer_type(session_data)
                
                if transformer_type == 'Trifásico':
                    trifasico_data.append(session_data)
                elif transformer_type == 'Monofásico':
                    monofasico_data.append(session_data)
        
        # Criar planilhas separadas
        if trifasico_data:
            create_transformer_data_sheet(writer, trifasico_data, 'Trifásicos')
        
        if monofasico_data:
            create_transformer_data_sheet(writer, monofasico_data, 'Monofásicos')
        
        # Criar planilha com dados detalhados de perdas
        create_losses_summary_sheet(writer, sessions)
        
        # Criar planilha com dados de ensaios
        create_tests_summary_sheet(writer, sessions)
    
    buffer.seek(0)
    return buffer


def create_summary_sheet(writer, sessions: List[Dict[str, Any]]):
    """Cria a planilha de resumo com estatísticas gerais."""
    
    summary_data = []
    summary_data.append(['Relatório Histórico Completo TTS', ''])
    summary_data.append(['Data de Geração', datetime.now().strftime('%d/%m/%Y %H:%M:%S')])
    summary_data.append(['Total de Sessões', len(sessions)])
    summary_data.append(['', ''])
    
    # Contar tipos de transformadores
    trifasico_count = 0
    monofasico_count = 0
    
    for session in sessions:
        session_data = extract_session_data(session)
        if session_data:
            transformer_type = get_transformer_type(session_data)
            if transformer_type == 'Trifásico':
                trifasico_count += 1
            elif transformer_type == 'Monofásico':
                monofasico_count += 1
    
    summary_data.append(['Estatísticas por Tipo:', ''])
    summary_data.append(['- Transformadores Trifásicos', trifasico_count])
    summary_data.append(['- Transformadores Monofásicos', monofasico_count])
    summary_data.append(['', ''])
    
    # Estatísticas de módulos
    modules_stats = count_modules_usage(sessions)
    summary_data.append(['Uso de Módulos:', ''])
    for module, count in modules_stats.items():
        summary_data.append([f'- {module}', count])
    
    df_summary = pd.DataFrame(summary_data, columns=['Item', 'Valor'])
    df_summary.to_excel(writer, sheet_name='Resumo', index=False)


def create_transformer_data_sheet(writer, transformer_data: List[Dict], sheet_name: str):
    """Cria planilha com dados básicos dos transformadores."""

    rows = []

    for data in transformer_data:
        if not isinstance(data, dict):
            continue

        transformer_inputs = data.get('transformerInputs', {})
        if not isinstance(transformer_inputs, dict):
            transformer_inputs = {}

        session_info = data.get('session_info', {})
        if not isinstance(session_info, dict):
            session_info = {}
        
        row = {
            'Sessão': session_info.get('session_name', ''),
            'Data Criação': session_info.get('created_at', ''),
            'Data Modificação': session_info.get('updated_at', ''),
            'Potência (MVA)': transformer_inputs.get('potencia_mva', ''),
            'Frequência (Hz)': transformer_inputs.get('frequencia', ''),
            'Tipo': transformer_inputs.get('tipo_transformador', ''),
            'Resfriamento': transformer_inputs.get('resfriamento', ''),
            'Tensão AT (kV)': transformer_inputs.get('tensao_at', ''),
            'Tensão BT (kV)': transformer_inputs.get('tensao_bt', ''),
            'Conexão AT': transformer_inputs.get('conexao_at', ''),
            'Conexão BT': transformer_inputs.get('conexao_bt', ''),
            'Classe Tensão AT (kV)': transformer_inputs.get('classe_tensao_at', ''),
            'Classe Tensão BT (kV)': transformer_inputs.get('classe_tensao_bt', ''),
            'NBI AT (kV)': transformer_inputs.get('nbi_at', ''),
            'NBI BT (kV)': transformer_inputs.get('nbi_bt', ''),
        }
        
        # Adicionar dados de perdas se disponíveis
        losses_data = data.get('losses', {})
        if losses_data and isinstance(losses_data, dict):
            form_data = losses_data.get('formData', {})
            if isinstance(form_data, dict):
                row.update({
                    'Perdas em Vazio (kW)': form_data.get('perdas_vazio_kw', ''),
                    'Perdas Carga Nom (kW)': form_data.get('perdas_carga_kw_u_nom', ''),
                    'Impedância (%)': form_data.get('impedancia', ''),
                    'Peso Núcleo (Ton)': form_data.get('peso_projeto_Ton', ''),
                })
        
        rows.append(row)
    
    if rows:
        df = pd.DataFrame(rows)
        df.to_excel(writer, sheet_name=sheet_name, index=False)


def create_losses_summary_sheet(writer, sessions: List[Dict[str, Any]]):
    """Cria planilha com resumo de dados de perdas."""
    
    rows = []
    
    for session in sessions:
        session_data = extract_session_data(session)
        if not session_data:
            continue

        losses_data = session_data.get('losses', {})
        if not losses_data or not isinstance(losses_data, dict):
            continue

        session_info = session_data.get('session_info', {})
        if not isinstance(session_info, dict):
            session_info = {}

        form_data = losses_data.get('formData', {})
        if not isinstance(form_data, dict):
            form_data = {}

        results_no_load = losses_data.get('resultsNoLoad', {})
        if not isinstance(results_no_load, dict):
            results_no_load = {}
        
        row = {
            'Sessão': session_info.get('session_name', ''),
            'Data': session_info.get('created_at', ''),
            'Tipo Transformador': get_transformer_type(session_data),
            'Perdas em Vazio Entrada (kW)': form_data.get('perdas_vazio_kw', ''),
            'Perdas em Vazio Calculada (kW)': results_no_load.get('perdas_vazio_kw_calculada', ''),
            'Corrente Excitação (%)': form_data.get('corrente_excitacao', ''),
            'Indução Núcleo (T)': form_data.get('inducao_nucleo', ''),
            'Peso Núcleo (Ton)': form_data.get('peso_projeto_Ton', ''),
            'Tipo Aço': form_data.get('tipo_aco', ''),
            'Perdas Carga Menor (kW)': form_data.get('perdas_carga_kw_u_min', ''),
            'Perdas Carga Nominal (kW)': form_data.get('perdas_carga_kw_u_nom', ''),
            'Perdas Carga Maior (kW)': form_data.get('perdas_carga_kw_u_max', ''),
            'Impedância (%)': form_data.get('impedancia', ''),
            'Temperatura Ref (°C)': form_data.get('temperatura_referencia', ''),
        }
        
        rows.append(row)
    
    if rows:
        df = pd.DataFrame(rows)
        df.to_excel(writer, sheet_name='Perdas', index=False)


def create_tests_summary_sheet(writer, sessions: List[Dict[str, Any]]):
    """Cria planilha com resumo de dados de ensaios."""
    
    rows = []
    
    for session in sessions:
        session_data = extract_session_data(session)
        if not session_data:
            continue
            
        session_info = session_data.get('session_info', {})
        
        # Dados de tensão aplicada
        applied_voltage = session_data.get('appliedVoltage', {})
        if applied_voltage and isinstance(applied_voltage, dict):
            form_data = applied_voltage.get('formData', {})
            if isinstance(form_data, dict):
                row = {
                    'Sessão': session_info.get('session_name', ''),
                    'Data': session_info.get('created_at', ''),
                    'Tipo Ensaio': 'Tensão Aplicada',
                    'Tensão Ensaio (kV)': form_data.get('tensao_ensaio', ''),
                    'Duração (s)': form_data.get('duracao_ensaio', ''),
                    'Frequência (Hz)': form_data.get('frequencia', ''),
                    'Observações': form_data.get('observacoes', ''),
                }
                rows.append(row)
        
        # Dados de tensão induzida
        induced_voltage = session_data.get('inducedVoltage', {})
        if induced_voltage:
            form_data = induced_voltage.get('formData', {})
            row = {
                'Sessão': session_info.get('session_name', ''),
                'Data': session_info.get('created_at', ''),
                'Tipo Ensaio': 'Tensão Induzida',
                'Tensão Ensaio (kV)': form_data.get('tensao_ensaio', ''),
                'Duração (s)': form_data.get('duracao_ensaio', ''),
                'Frequência (Hz)': form_data.get('frequencia_ensaio', ''),
                'Observações': form_data.get('observacoes', ''),
            }
            rows.append(row)
    
    if rows:
        df = pd.DataFrame(rows)
        df.to_excel(writer, sheet_name='Ensaios', index=False)


def extract_session_data(session: Dict[str, Any]) -> Dict[str, Any]:
    """Extrai e organiza os dados de uma sessão."""
    try:
        session_data_str = session.get('session_data', '{}')
        if isinstance(session_data_str, str):
            session_data = json.loads(session_data_str)
        else:
            session_data = session_data_str
        
        # Adicionar informações da sessão
        session_info = {
            'session_id': session.get('session_id', ''),
            'session_name': session_data.get('session_name', session.get('session_id', '')),
            'description': session.get('description', ''),
            'created_at': session.get('created_at', ''),
            'updated_at': session.get('updated_at', ''),
        }
        
        # Extrair dados dos stores
        stores = session_data.get('stores', {})
        result = {'session_info': session_info}

        # Verificar se stores é um dicionário ou lista
        if isinstance(stores, dict):
            for store_name, store_data in stores.items():
                if store_data:
                    result[store_name] = store_data
        elif isinstance(stores, list):
            # Se for uma lista, tentar extrair dados de cada item
            for i, store_item in enumerate(stores):
                if isinstance(store_item, dict):
                    for store_name, store_data in store_item.items():
                        if store_data:
                            result[store_name] = store_data
        
        return result
        
    except (json.JSONDecodeError, KeyError, TypeError) as e:
        print(f"Erro ao extrair dados da sessão: {e}")
        return {}


def get_transformer_type(session_data: Dict[str, Any]) -> str:
    """Determina o tipo do transformador baseado nos dados."""
    transformer_inputs = session_data.get('transformerInputs', {})
    
    # Buscar em diferentes estruturas possíveis
    form_data = transformer_inputs.get('formData', {})
    inputs = transformer_inputs.get('inputs', {})
    basic_data = transformer_inputs.get('basicData', {})
    
    # Combinar todas as fontes
    combined_data = {**form_data, **inputs, **basic_data, **transformer_inputs}
    
    transformer_type = combined_data.get('tipo_transformador', '')
    
    if 'trifásico' in transformer_type.lower() or 'trifasico' in transformer_type.lower():
        return 'Trifásico'
    elif 'monofásico' in transformer_type.lower() or 'monofasico' in transformer_type.lower():
        return 'Monofásico'
    else:
        return 'Não Definido'


def count_modules_usage(sessions: List[Dict[str, Any]]) -> Dict[str, int]:
    """Conta quantas vezes cada módulo foi usado."""
    module_names = {
        'transformerInputs': 'Dados Básicos',
        'losses': 'Perdas',
        'appliedVoltage': 'Tensão Aplicada',
        'inducedVoltage': 'Tensão Induzida'
    }
    
    counts = {name: 0 for name in module_names.values()}
    
    for session in sessions:
        session_data = extract_session_data(session)
        if session_data:
            for module_key, module_name in module_names.items():
                if module_key in session_data and session_data[module_key]:
                    counts[module_name] += 1
    
    return counts
